document.addEventListener('DOMContentLoaded', () => {
    const startUrlInput = document.getElementById('startUrl');
    const startButton = document.getElementById('startButton');
    const stopButton = document.getElementById('stopButton'); // الزر غير مستخدم حاليًا في هذا المنطق
    const statusText = document.getElementById('statusText');
    const progressBar = document.getElementById('progressBar');
    const movieList = document.getElementById('movieList');

    let resultCheckInterval;

    startButton.addEventListener('click', () => {
        const url = startUrlInput.value;
        if (!url) {
            alert('الرجاء إدخال رابط للبدء.');
            return;
        }
        startScraping(url);
    });

    async function startScraping(url) {
        statusText.textContent = `جاري إرسال طلب البحث لـ ${url}...`;
        startButton.disabled = true;
        stopButton.disabled = false; // يمكن تفعيله لإضافة منطق إيقاف حقيقي لاحقًا
        progressBar.value = 0;
        movieList.innerHTML = '<li>جاري البحث...</li>';

        try {
            const response = await fetch('/start-scraping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'حدث خطأ غير معروف في الخادم.');
            }

            statusText.textContent = 'بدأت عملية البحث في الخلفية. جاري التحقق من النتائج...';
            
            // بدء التحقق الدوري من النتائج
            let progress = 0;
            progressBar.value = progress;
            resultCheckInterval = setInterval(async () => {
                // تحديث شريط التقدم بشكل وهمي
                if (progress < 90) {
                    progress += 5;
                    progressBar.value = progress;
                }
                
                const resultResponse = await fetch('/get-results');
                const movies = await resultResponse.json();
                
                // عرض النتائج فور توفرها
                if (movies.length > 0) {
                    updateMovieList(movies);
                }
                // ملاحظة: لا يمكننا معرفة متى تنتهي العملية 100% بهذا التصميم
                // لذا سنستمر في التحقق. يمكن إيقاف هذا عند ضغط المستخدم على زر "إيقاف"
                
            }, 5000); // تحقق كل 5 ثوانٍ

        } catch (error) {
            statusText.textContent = `فشل: ${error.message}`;
            movieList.innerHTML = `<li>حدث خطأ: ${error.message}</li>`;
            startButton.disabled = false;
            stopButton.disabled = true;
        }
    }

    function updateMovieList(movies) {
        movieList.innerHTML = ''; // مسح القائمة قبل التحديث
        if (movies.length === 0) {
            movieList.innerHTML = '<li>لم يتم العثور على أفلام بعد.</li>';
        } else {
            movies.forEach(movie => {
                const li = document.createElement('li');
                const movieName = document.createElement('span');
                movieName.textContent = movie.name;
                
                const movieLink = document.createElement('a');
                movieLink.href = movie.link;
                movieLink.textContent = '🔗 رابط الفيلم';
                movieLink.target = '_blank';

                li.appendChild(movieName);
                li.appendChild(movieLink);
                movieList.appendChild(li);
            });
            // عند العثور على نتائج، يمكننا افتراض أن العملية انتهت
            clearInterval(resultCheckInterval);
            progressBar.value = 100;
            statusText.textContent = `اكتمل! تم العثور على ${movies.length} فيلم.`;
            startButton.disabled = false;
            stopButton.disabled = true;
        }
    }

    // منطق زر الإيقاف (اختياري ومتقدم)
    stopButton.addEventListener('click', () => {
        if (resultCheckInterval) {
            clearInterval(resultCheckInterval);
        }
        statusText.textContent = 'تم إيقاف التحقق من النتائج.';
        startButton.disabled = false;
        stopButton.disabled = true;
        progressBar.value = 0;
        // ملاحظة: هذا لا يوقف عملية البايثون في الخلفية،
        // بل يوقف فقط التحقق من النتائج من جانب العميل.
    });
});
