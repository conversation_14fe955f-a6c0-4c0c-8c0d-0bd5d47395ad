# نظام الاستخراج المتقدم للأفلام والمسلسلات

## 🚀 الميزات الجديدة

### 1. قواعد الاستخراج المتعددة
- **8 قواعد استخراج مختلفة** تعمل تلقائياً
- **تقييم جودة تلقائي** لاختيار أفضل قاعدة
- **دعم المواقع العربية والأجنبية**
- **تعرف ذكي على بطاقات الأفلام**

### 2. التعرف التلقائي على الصفحات التالية
- **5 استراتيجيات مختلفة** للعثور على روابط الصفحات التالية
- **البحث بالنص** (التالي، next، المزيد، more)
- **البحث بالكلاسات** (next-page، pagination-next)
- **البحث بالأرقام** (أرقام الصفحات)
- **البحث بالأسهم والرموز** (»، >، →)
- **البحث بمعاملات URL** (page=، p=، offset=)

### 3. نظام التقييم الذكي
- **تقييم جودة العناوين** (طول، كلمات مفتاحية)
- **تقييم جودة الصور** (وجود صور حقيقية)
- **تقييم جودة الروابط** (صحة الروابط)
- **تقييم التفرد** (عدم التكرار)
- **نقاط إضافية** للمحتوى عالي الجودة

## 📋 قواعد الاستخراج المتاحة

### 1. `rule_generic_movie_cards`
- بطاقات الأفلام العامة
- `.movie-card`, `.film-card`, `.content-card`

### 2. `rule_arabic_movie_sites`
- المواقع العربية المتخصصة
- `.فيلم`, `.مسلسل`, `.حلقة`

### 3. `rule_wordpress_themes`
- مواقع WordPress
- `.post`, `.entry`, `.article`

### 4. `rule_bootstrap_cards`
- بطاقات Bootstrap
- `.card`, `.media`, `.thumbnail`

### 5. `rule_flex_grid_layouts`
- التخطيطات المرنة
- `.grid-item`, `.flex-item`, `.col`

### 6. `rule_list_based_layouts`
- التخطيطات القائمة على القوائم
- عناصر `<li>`

### 7. `rule_table_based_layouts`
- التخطيطات القائمة على الجداول
- عناصر `<tr>`

### 8. `rule_custom_movie_sites`
- قواعد مخصصة للمواقع المعروفة
- cimanow.cc، asd.homes

## 🛠️ الملفات الجديدة

### `extraction_config.py`
ملف الإعدادات المتقدمة:
- إعدادات عامة للاستخراج
- قواعد مخصصة للمواقع
- كلمات مفتاحية للمحتوى
- أنماط URL للتعرف على الصفحات
- قواعد التنظيف والتصفية

### `test_extraction.py`
ملف اختبار النظام:
- اختبار قواعد الاستخراج
- اختبار المواقع الحقيقية
- اختبار تنظيف العناوين
- حفظ نتائج الاختبار

## 🎯 كيفية الاستخدام

### 1. الاستخدام العادي
```python
# تشغيل الخادم
python app.py

# إرسال طلب POST إلى /api/scrape
{
    "start_url": "https://example.com/movies",
    "max_depth": 3,
    "delay": 1,
    "link_filter": ""
}
```

### 2. اختبار النظام
```bash
# تشغيل الاختبارات
python test_extraction.py
```

### 3. تخصيص الإعدادات
```python
# تعديل ملف extraction_config.py
EXTRACTION_SETTINGS = {
    'min_quality_threshold': 0.3,  # الحد الأدنى للجودة
    'max_items_per_page': 100,     # الحد الأقصى للعناصر
    # ...
}
```

## 📊 مثال على النتائج

```json
{
  "movies_info": [
    {
      "movies_name": "فيلم رائع",
      "movies_img": "https://example.com/poster.jpg",
      "movies_href": "https://example.com/watch.php?id=123"
    }
  ],
  "series_info": [
    {
      "series_name": "مسلسل مثير",
      "series_img": "https://example.com/series.jpg", 
      "series_href": "https://example.com/series/123"
    }
  ]
}
```

## 🔧 الإعدادات المتقدمة

### تخصيص موقع معين
```python
SITE_SPECIFIC_RULES = {
    'yoursite.com': {
        'selectors': ['.custom-movie-card'],
        'title_selectors': ['.custom-title'],
        'image_selectors': ['.custom-image img'],
        'link_selectors': ['.custom-link a']
    }
}
```

### تخصيص كلمات مفتاحية
```python
CONTENT_KEYWORDS = {
    'movies': ['movie', 'film', 'فيلم'],
    'series': ['series', 'مسلسل'],
    'skip_keywords': ['login', 'admin']
}
```

## 📈 مؤشرات الجودة

### معايير التقييم
- **جودة العناوين**: 30%
- **جودة الصور**: 20%
- **جودة الروابط**: 30%
- **التفرد**: 20%

### نقاط إضافية
- وجود كلمات مفتاحية للأفلام: +0.1
- وجود معاملات مشاهدة: +0.1
- كمية معقولة (5-50 عنصر): +0.1

## 🚨 نصائح للاستخدام

### 1. اختيار العمق المناسب
- **العمق 1-2**: للمواقع الصغيرة
- **العمق 3-4**: للمواقع المتوسطة
- **العمق 5+**: للمواقع الكبيرة (احذر من الحمل الزائد)

### 2. تحسين الأداء
- استخدم تأخير مناسب (1-3 ثواني)
- راقب استهلاك الذاكرة
- احفظ النتائج دورياً

### 3. التعامل مع الأخطاء
- تحقق من سجلات الأخطاء
- استخدم ملف الاختبار للتشخيص
- اضبط الإعدادات حسب الحاجة

## 🔍 استكشاف الأخطاء

### مشكلة: لا يتم استخراج محتوى
**الحل**: 
1. تحقق من سجلات الجودة
2. جرب قواعد مختلفة
3. اضبط الحد الأدنى للجودة

### مشكلة: عناوين غير صحيحة
**الحل**:
1. فعّل التنظيف المتقدم
2. أضف كلمات للإزالة
3. اضبط أنماط التنظيف

### مشكلة: لا يجد الصفحات التالية
**الحل**:
1. تحقق من استراتيجيات البحث
2. أضف كلمات مفتاحية جديدة
3. فحص بنية HTML للموقع

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملف الاختبار
2. تحقق من السجلات
3. اضبط الإعدادات
4. استخدم وضع التشخيص

---

**تم تطوير هذا النظام ليكون مرناً وقابلاً للتخصيص لجميع أنواع مواقع الأفلام والمسلسلات.**
