body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f2f5;
    color: #333;
    margin: 0;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
}

.container {
    width: 100%;
    max-width: 800px;
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

header {
    text-align: center;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

header h1 {
    color: #0056b3;
    margin: 0;
}

.controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#startUrl {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    color: #fff;
}

#startButton {
    background-color: #28a745;
}

#startButton:hover {
    background-color: #218838;
}

#stopButton {
    background-color: #dc3545;
}

#stopButton:hover {
    background-color: #c82333;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.status {
    margin-bottom: 20px;
}

#progressBar {
    width: 100%;
    height: 10px;
    border-radius: 5px;
}

.results h2 {
    text-align: center;
    color: #0056b3;
}

#movieList {
    list-style-type: none;
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
}

#movieList li {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#movieList li:last-child {
    border-bottom: none;
}

#movieList a {
    text-decoration: none;
    color: #007bff;
    font-weight: bold;
}

#movieList a:hover {
    text-decoration: underline;
}

footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    color: #888;
}
