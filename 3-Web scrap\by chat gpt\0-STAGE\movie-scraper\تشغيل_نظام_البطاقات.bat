@echo off
chcp 65001 >nul
title نظام استخراج بطاقات الأفلام والمسلسلات المتقدم

echo.
echo ========================================================
echo 🎬 نظام استخراج بطاقات الأفلام والمسلسلات المتقدم
echo ========================================================
echo.
echo ✨ الميزات الجديدة:
echo   - استخراج البطاقات فقط (وليس صفحات العرض)
echo   - فلترة نوع المحتوى (أفلام/مسلسلات/الكل)
echo   - البقاء في نفس التصنيف المدخل
echo   - 8 قواعد استخراج ذكية
echo   - تقييم جودة تلقائي
echo.
echo 📁 تنظيم الملفات:
echo   - results/     ملفات النتائج JSON
echo   - docs/        ملفات التوثيق
echo.

:MENU
echo ========================================================
echo اختر العملية المطلوبة:
echo ========================================================
echo.
echo [1] 🚀 تشغيل الخادم (الواجهة الويب)
echo [2] 🧪 اختبار النظام السريع
echo [3] 🌐 اختبار مع مواقع حقيقية
echo [4] 📊 عرض ملفات النتائج
echo [5] 📖 فتح دليل الاستخدام
echo [6] ❌ خروج
echo.
set /p choice="أدخل رقم الخيار (1-6): "

if "%choice%"=="1" goto START_SERVER
if "%choice%"=="2" goto QUICK_TEST
if "%choice%"=="3" goto REAL_TEST
if "%choice%"=="4" goto SHOW_RESULTS
if "%choice%"=="5" goto SHOW_DOCS
if "%choice%"=="6" goto EXIT

echo ❌ خيار غير صحيح، حاول مرة أخرى
pause
goto MENU

:START_SERVER
echo.
echo 🚀 بدء تشغيل الخادم...
echo ========================================================
echo.
echo 📌 بعد بدء الخادم:
echo   - افتح المتصفح على: http://localhost:5000
echo   - أدخل رابط صفحة التصنيف
echo   - اختر نوع المحتوى (أفلام/مسلسلات/الكل)
echo   - ابدأ الاستخراج
echo.
echo 🛑 لإيقاف الخادم: اضغط Ctrl+C
echo.
pause
python app.py
goto MENU

:QUICK_TEST
echo.
echo 🧪 تشغيل الاختبار السريع...
echo ========================================================
echo.
echo 📋 سيتم اختبار:
echo   - استخراج البطاقات من HTML تجريبي
echo   - التحقق من صحة روابط البطاقات
echo   - فلترة نوع المحتوى
echo   - البحث عن الصفحات التالية
echo.
python test_card_extraction.py
echo.
echo ✅ انتهى الاختبار السريع
pause
goto MENU

:REAL_TEST
echo.
echo 🌐 اختبار مع مواقع حقيقية...
echo ========================================================
echo.
echo ⚠️ تحذير: هذا الاختبار يتطلب اتصال إنترنت
echo.
echo 📋 المواقع التي سيتم اختبارها:
echo   - https://a.asd.homes/category/foreign-movies-6/
echo   - https://cimanow.cc/category/افلام-اجنبية/
echo.
set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i "%confirm%" neq "y" goto MENU

echo y | python test_card_extraction.py
echo.
echo ✅ انتهى اختبار المواقع الحقيقية
echo 📁 تحقق من مجلد results/ للنتائج
pause
goto MENU

:SHOW_RESULTS
echo.
echo 📊 عرض ملفات النتائج...
echo ========================================================
echo.
if not exist "results" (
    echo ❌ مجلد النتائج غير موجود
    echo 💡 قم بتشغيل الاستخراج أولاً لإنشاء النتائج
    pause
    goto MENU
)

echo 📁 ملفات النتائج الموجودة:
echo.
dir /b results\*.json 2>nul
if errorlevel 1 (
    echo ❌ لا توجد ملفات نتائج
    echo 💡 قم بتشغيل الاستخراج أولاً
) else (
    echo.
    echo 💡 يمكنك فتح هذه الملفات بأي محرر نصوص
    echo 📂 فتح مجلد النتائج...
    start "" "results"
)
pause
goto MENU

:SHOW_DOCS
echo.
echo 📖 فتح ملفات التوثيق...
echo ========================================================
echo.
if not exist "docs" (
    echo ❌ مجلد التوثيق غير موجود
    pause
    goto MENU
)

echo 📚 ملفات التوثيق المتاحة:
echo.
dir /b docs\*.md 2>nul
if errorlevel 1 (
    echo ❌ لا توجد ملفات توثيق
) else (
    echo.
    echo 📂 فتح مجلد التوثيق...
    start "" "docs"
    
    echo.
    echo 📋 الملفات الرئيسية:
    echo   - دليل_استخراج_البطاقات.md (دليل شامل)
    echo   - README_ADVANCED.md (التوثيق التقني)
    echo   - ملخص_الميزات_الجديدة.md (الميزات الجديدة)
)
pause
goto MENU

:EXIT
echo.
echo 👋 شكراً لاستخدام نظام استخراج بطاقات الأفلام والمسلسلات!
echo.
echo 📌 ملخص الميزات:
echo   ✅ استخراج البطاقات فقط (وليس صفحات العرض)
echo   ✅ فلترة نوع المحتوى (أفلام/مسلسلات/الكل)
echo   ✅ البقاء في نفس التصنيف
echo   ✅ 8 قواعد استخراج ذكية
echo   ✅ تقييم جودة تلقائي
echo   ✅ تنظيم أفضل للملفات
echo.
echo 🎬 النظام جاهز لاستخراج بطاقات الأفلام بكفاءة عالية!
echo.
pause
exit
