# -*- coding: utf-8 -*-
"""
ملف اختبار نظام الاستخراج المتقدم
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import json
import time
from app import ExtractionRules, extract_content_info_with_details, find_next_page_links
from extraction_config import get_site_config, is_valid_content_url, clean_title

def test_site_extraction(url, max_pages=3):
    """اختبار استخراج المحتوى من موقع معين"""
    print(f"\n🚀 اختبار الاستخراج من: {url}")
    print("=" * 60)
    
    visited_pages = set()
    all_results = []
    page_count = 0
    
    pages_to_visit = [url]
    
    while pages_to_visit and page_count < max_pages:
        current_url = pages_to_visit.pop(0)
        
        if current_url in visited_pages:
            continue
            
        visited_pages.add(current_url)
        page_count += 1
        
        print(f"\n📄 صفحة {page_count}: {current_url}")
        print("-" * 40)
        
        try:
            # تحميل الصفحة
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(current_url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # استخراج المحتوى
            content_items, extraction_info = extract_content_info_with_details(soup, current_url)
            
            print(f"✅ تم استخراج {len(content_items)} عنصر")
            
            if extraction_info:
                print(f"🎯 أفضل قاعدة: {extraction_info['best_rule']}")
                print(f"📊 جودة الاستخراج: {extraction_info['best_score']:.2f}")
                print(f"🔧 عدد القواعد المختبرة: {extraction_info['total_rules_tested']}")
                
                # عرض تفاصيل القواعد
                print("\n📋 تفاصيل القواعد:")
                for result in extraction_info['rule_results'][:3]:  # أفضل 3 قواعد
                    print(f"  - {result['rule']}: {result['count']} عنصر (جودة: {result['quality_score']:.2f})")
            
            # عرض عينة من النتائج
            print(f"\n🎬 عينة من النتائج:")
            for i, item in enumerate(content_items[:5]):  # أول 5 عناصر
                print(f"  {i+1}. {item['title'][:50]}...")
                print(f"     النوع: {item['type']}")
                print(f"     الرابط: {item['link'][:60]}...")
                if item['imageUrl'] != "https://via.placeholder.com/300x400?text=Movie":
                    print(f"     الصورة: ✅ موجودة")
                else:
                    print(f"     الصورة: ❌ افتراضية")
                print()
            
            all_results.extend(content_items)
            
            # البحث عن الصفحات التالية
            if page_count < max_pages:
                next_links = find_next_page_links(soup, current_url)
                print(f"\n🔗 وجد {len(next_links)} رابط للصفحات التالية")
                
                for next_link in next_links[:2]:  # أول رابطين فقط
                    if next_link not in visited_pages:
                        pages_to_visit.append(next_link)
                        print(f"  ➕ أضيف: {next_link[:60]}...")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل الصفحة: {str(e)}")
            continue
        
        # تأخير بين الصفحات
        if page_count < max_pages:
            print(f"\n⏳ انتظار 2 ثانية...")
            time.sleep(2)
    
    # ملخص النتائج
    print(f"\n📊 ملخص النتائج:")
    print(f"  - عدد الصفحات المعالجة: {page_count}")
    print(f"  - إجمالي العناصر: {len(all_results)}")
    
    # تصنيف النتائج
    movies = [item for item in all_results if item['type'] == 'movie']
    series = [item for item in all_results if item['type'] == 'series']
    
    print(f"  - الأفلام: {len(movies)}")
    print(f"  - المسلسلات: {len(series)}")
    
    # حفظ النتائج
    output_file = f"test_results_{urlparse(url).netloc.replace('.', '_')}.json"
    
    results_data = {
        'source_url': url,
        'pages_processed': page_count,
        'total_items': len(all_results),
        'movies_count': len(movies),
        'series_count': len(series),
        'movies': movies,
        'series': series,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        print(f"💾 تم حفظ النتائج في: {output_file}")
    except Exception as e:
        print(f"⚠️ خطأ في حفظ النتائج: {str(e)}")
    
    return all_results

def test_extraction_rules():
    """اختبار قواعد الاستخراج المختلفة"""
    print("\n🧪 اختبار قواعد الاستخراج")
    print("=" * 40)
    
    # HTML تجريبي
    test_html = """
    <html>
    <body>
        <div class="movie-card">
            <a href="/movie/test-movie-1">
                <img src="/images/movie1.jpg" alt="Test Movie 1">
                <h3>Test Movie 1</h3>
            </a>
        </div>
        
        <div class="post">
            <h2><a href="/watch.php?id=123">فيلم تجريبي 2</a></h2>
            <img src="/posters/movie2.jpg" alt="فيلم تجريبي 2">
        </div>
        
        <article class="entry">
            <a href="/series/test-series">
                <div class="title">مسلسل تجريبي</div>
                <img data-src="/images/series1.jpg">
            </a>
        </article>
    </body>
    </html>
    """
    
    soup = BeautifulSoup(test_html, 'html.parser')
    base_url = "https://example.com"
    
    # اختبار كل قاعدة
    rules = ExtractionRules.get_all_rules()
    
    for rule in rules:
        try:
            items = rule(soup, base_url)
            print(f"📋 {rule.__name__}: {len(items)} عنصر")
            
            for item in items:
                print(f"  - {item['title']} ({item['type']})")
        
        except Exception as e:
            print(f"❌ {rule.__name__}: خطأ - {str(e)}")
    
    # اختبار الاستخراج المدمج
    print(f"\n🔄 الاستخراج المدمج:")
    content_items, extraction_info = extract_content_info_with_details(soup, base_url)
    
    print(f"✅ إجمالي العناصر: {len(content_items)}")
    if extraction_info:
        print(f"🎯 أفضل قاعدة: {extraction_info['best_rule']}")
        print(f"📊 الجودة: {extraction_info['best_score']:.2f}")

def test_title_cleaning():
    """اختبار تنظيف العناوين"""
    print("\n🧹 اختبار تنظيف العناوين")
    print("=" * 40)
    
    test_titles = [
        "فيلم رائع - مشاهدة اونلاين",
        "Test Movie (2023) - Watch Online Free",
        "   مسلسل جميل   - تحميل مجاني   ",
        "Movie Title - Download HD",
        "عنوان قصير",
        "",
        "عنوان طويل جداً " * 10
    ]
    
    for title in test_titles:
        cleaned = clean_title(title)
        print(f"الأصلي: '{title}'")
        print(f"المنظف: '{cleaned}'")
        print(f"صالح: {'✅' if cleaned else '❌'}")
        print("-" * 30)

if __name__ == "__main__":
    print("🎬 اختبار نظام الاستخراج المتقدم")
    print("=" * 50)
    
    # اختبار قواعد الاستخراج
    test_extraction_rules()
    
    # اختبار تنظيف العناوين
    test_title_cleaning()
    
    # اختبار مواقع حقيقية (اختياري)
    test_sites = [
        "https://a.asd.homes/category/foreign-movies-6/",
        "https://cimanow.cc/category/%d8%a7%d9%81%d9%84%d8%a7%d9%85-%d8%a7%d8%ac%d9%86%d8%a8%d9%8a%d8%a9/"
    ]
    
    print(f"\n🌐 هل تريد اختبار المواقع الحقيقية؟ (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        for site_url in test_sites:
            try:
                test_site_extraction(site_url, max_pages=2)
            except Exception as e:
                print(f"❌ خطأ في اختبار {site_url}: {str(e)}")
    
    print(f"\n✅ انتهى الاختبار!")
