# -*- coding: utf-8 -*-
"""
اختبار نظام استخراج بطاقات الأفلام والمسلسلات المحدث
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import json
import time
import os
from app import (
    extract_content_info_with_details, 
    find_category_pagination_links,
    _is_card_link,
    _is_same_category_pagination
)

def test_card_extraction_system():
    """اختبار نظام استخراج البطاقات الجديد"""
    print("🎬 اختبار نظام استخراج بطاقات الأفلام والمسلسلات")
    print("=" * 60)
    
    # HTML تجريبي لصفحة تصنيف أفلام
    category_html = """
    <html>
    <head><title>أفلام أجنبية - الصفحة 1</title></head>
    <body>
        <div class="category-header">
            <h1>أفلام أجنبية</h1>
        </div>
        
        <!-- بطاقات الأفلام -->
        <div class="movie-grid">
            <div class="movie-card">
                <a href="/movie/avengers-endgame-2019">
                    <img src="/posters/avengers-endgame.jpg" alt="Avengers: Endgame (2019)">
                    <h2>Avengers: Endgame</h2>
                </a>
            </div>
            
            <div class="movie-card">
                <a href="/movie/joker-2019">
                    <img src="/posters/joker.jpg" alt="Joker (2019)">
                    <h2>Joker</h2>
                </a>
            </div>
            
            <div class="movie-card">
                <a href="/movie/spider-man-2021">
                    <img src="/posters/spiderman.jpg" alt="Spider-Man: No Way Home">
                    <h2>Spider-Man: No Way Home</h2>
                </a>
            </div>
            
            <!-- بطاقة مسلسل -->
            <div class="series-card">
                <a href="/series/breaking-bad">
                    <img src="/posters/breaking-bad.jpg" alt="Breaking Bad - مسلسل">
                    <h2>Breaking Bad - مسلسل</h2>
                </a>
            </div>
        </div>
        
        <!-- روابط يجب تجنبها (صفحات عرض) -->
        <div class="watch-links" style="display:none;">
            <a href="/watch.php?id=123">مشاهدة Avengers</a>
            <a href="/play/joker-stream">تشغيل Joker</a>
            <a href="/view/spiderman-online">عرض Spider-Man</a>
        </div>
        
        <!-- روابط التصفح -->
        <div class="pagination">
            <a href="/category/foreign-movies?page=1" class="current">1</a>
            <a href="/category/foreign-movies?page=2">2</a>
            <a href="/category/foreign-movies?page=3">3</a>
            <a href="/category/foreign-movies?page=2" class="next">التالي</a>
        </div>
        
        <!-- روابط خارج التصنيف (يجب تجنبها) -->
        <div class="other-categories">
            <a href="/category/arabic-movies">أفلام عربية</a>
            <a href="/category/series">مسلسلات</a>
        </div>
    </body>
    </html>
    """
    
    soup = BeautifulSoup(category_html, 'html.parser')
    base_url = "https://example-movies.com/category/foreign-movies"
    base_category_path = "/category/foreign-movies"
    
    print("1️⃣ اختبار استخراج البطاقات:")
    print("-" * 30)
    
    # اختبار الاستخراج
    content_items, extraction_info = extract_content_info_with_details(soup, base_url)
    
    print(f"✅ تم استخراج {len(content_items)} بطاقة")
    
    if extraction_info:
        print(f"🎯 أفضل قاعدة: {extraction_info['best_rule']}")
        print(f"📊 جودة الاستخراج: {extraction_info['best_score']:.2f}")
    
    # عرض البطاقات المستخرجة
    movies_count = 0
    series_count = 0
    
    print(f"\n🎬 البطاقات المستخرجة:")
    for i, item in enumerate(content_items, 1):
        print(f"  {i}. العنوان: {item['title']}")
        print(f"     النوع: {item['type']}")
        print(f"     الرابط: {item['link']}")
        print(f"     بطاقة صالحة: {'✅' if _is_card_link(item['link'], base_category_path) else '❌'}")
        
        if item['type'] == 'movie':
            movies_count += 1
        else:
            series_count += 1
        print()
    
    print(f"📊 الإحصائيات:")
    print(f"  - الأفلام: {movies_count}")
    print(f"  - المسلسلات: {series_count}")
    print(f"  - المجموع: {len(content_items)}")
    
    print(f"\n2️⃣ اختبار البحث عن الصفحات التالية:")
    print("-" * 30)
    
    # اختبار البحث عن الصفحات التالية
    next_links = find_category_pagination_links(soup, base_url, base_category_path)
    
    print(f"✅ وجد {len(next_links)} رابط للصفحات التالية:")
    for i, link in enumerate(next_links, 1):
        print(f"  {i}. {link}")
        print(f"     في نفس التصنيف: {'✅' if _is_same_category_pagination(link, base_url) else '❌'}")
    
    print(f"\n3️⃣ اختبار فلترة نوع المحتوى:")
    print("-" * 30)
    
    # اختبار فلترة الأفلام فقط
    movies_only = [item for item in content_items if item['type'] == 'movie']
    print(f"🎬 الأفلام فقط: {len(movies_only)} عنصر")
    
    # اختبار فلترة المسلسلات فقط
    series_only = [item for item in content_items if item['type'] == 'series']
    print(f"📺 المسلسلات فقط: {len(series_only)} عنصر")
    
    return {
        'total_cards': len(content_items),
        'movies_count': movies_count,
        'series_count': series_count,
        'next_pages': len(next_links),
        'extraction_quality': extraction_info['best_score'] if extraction_info else 0,
        'cards': content_items,
        'next_links': next_links
    }

def test_card_link_validation():
    """اختبار التحقق من صحة روابط البطاقات"""
    print(f"\n4️⃣ اختبار التحقق من صحة روابط البطاقات:")
    print("-" * 30)
    
    base_category_path = "/category/foreign-movies"
    
    test_links = [
        # روابط بطاقات صالحة
        ("https://example.com/movie/avengers-2019", True, "رابط بطاقة فيلم"),
        ("https://example.com/series/breaking-bad", True, "رابط بطاقة مسلسل"),
        ("https://example.com/category/foreign-movies/page/2", True, "صفحة في نفس التصنيف"),
        ("https://example.com/film.php?id=123", True, "رابط بطاقة بمعامل"),
        
        # روابط صفحات عرض (يجب تجنبها)
        ("https://example.com/watch.php?id=123", False, "صفحة مشاهدة"),
        ("https://example.com/play/movie-stream", False, "صفحة تشغيل"),
        ("https://example.com/view/online-movie", False, "صفحة عرض"),
        ("https://example.com/player.php?v=456", False, "صفحة مشغل"),
        
        # روابط خارج التصنيف (يجب تجنبها)
        ("https://example.com/category/arabic-movies", False, "تصنيف مختلف"),
        ("https://example.com/home", False, "صفحة رئيسية"),
    ]
    
    for link, expected, description in test_links:
        result = _is_card_link(link, base_category_path)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {description}: {link}")
        if result != expected:
            print(f"      متوقع: {expected}, الفعلي: {result}")

def test_with_real_site(url, max_pages=2):
    """اختبار مع موقع حقيقي"""
    print(f"\n5️⃣ اختبار مع موقع حقيقي:")
    print("-" * 30)
    print(f"🌐 الموقع: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        base_category_path = urlparse(url).path
        
        # استخراج البطاقات
        content_items, extraction_info = extract_content_info_with_details(soup, url)
        
        print(f"✅ تم استخراج {len(content_items)} بطاقة")
        
        if extraction_info:
            print(f"🎯 أفضل قاعدة: {extraction_info['best_rule']}")
            print(f"📊 جودة الاستخراج: {extraction_info['best_score']:.2f}")
        
        # عرض عينة من البطاقات
        print(f"\n📋 عينة من البطاقات:")
        for i, item in enumerate(content_items[:5], 1):
            print(f"  {i}. {item['title'][:50]}...")
            print(f"     النوع: {item['type']}")
            print(f"     بطاقة صالحة: {'✅' if _is_card_link(item['link'], base_category_path) else '❌'}")
        
        # البحث عن الصفحات التالية
        next_links = find_category_pagination_links(soup, url, base_category_path)
        print(f"\n🔗 وجد {len(next_links)} رابط للصفحات التالية")
        
        # حفظ النتائج
        results_dir = "results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
        
        results = {
            'test_info': {
                'url': url,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_cards': len(content_items),
                'extraction_quality': extraction_info['best_score'] if extraction_info else 0,
                'best_rule': extraction_info['best_rule'] if extraction_info else 'unknown'
            },
            'cards': content_items,
            'next_links': next_links
        }
        
        filename = os.path.join(results_dir, f"test_real_site_{urlparse(url).netloc.replace('.', '_')}.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ النتائج في: {filename}")
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الموقع: {str(e)}")
        return None

if __name__ == "__main__":
    print("🚀 بدء اختبار نظام استخراج البطاقات المحدث")
    print("=" * 60)
    
    # اختبار النظام الأساسي
    basic_results = test_card_extraction_system()
    
    # اختبار التحقق من الروابط
    test_card_link_validation()
    
    # اختبار مع مواقع حقيقية (اختياري)
    print(f"\n🌐 هل تريد اختبار مع مواقع حقيقية؟ (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        test_sites = [
            "https://a.asd.homes/category/foreign-movies-6/",
            "https://cimanow.cc/category/%d8%a7%d9%81%d9%84%d8%a7%d9%85-%d8%a7%d8%ac%d9%86%d8%a8%d9%8a%d8%a9/"
        ]
        
        for site_url in test_sites:
            try:
                test_with_real_site(site_url)
                time.sleep(3)  # تأخير بين المواقع
            except Exception as e:
                print(f"❌ خطأ في اختبار {site_url}: {str(e)}")
    
    print(f"\n✅ انتهى اختبار نظام استخراج البطاقات!")
    print(f"📊 ملخص النتائج:")
    print(f"  - البطاقات المستخرجة: {basic_results['total_cards']}")
    print(f"  - الأفلام: {basic_results['movies_count']}")
    print(f"  - المسلسلات: {basic_results['series_count']}")
    print(f"  - الصفحات التالية: {basic_results['next_pages']}")
    print(f"  - جودة الاستخراج: {basic_results['extraction_quality']:.2f}")
