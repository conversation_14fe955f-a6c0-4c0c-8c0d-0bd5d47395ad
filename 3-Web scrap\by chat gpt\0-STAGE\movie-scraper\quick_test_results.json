{"test_info": {"total_items": 3, "extraction_quality": 1.0, "best_rule": "rule_generic_movie_cards", "next_pages_found": 2}, "movies": [{"title": "Avengers: Endgame", "imageUrl": "https://example-movies.com/posters/avengers.jpg", "link": "https://example-movies.com/movie/avengers-endgame", "type": "movie"}, {"title": "Joker (2019)", "imageUrl": "https://example-movies.com/posters/joker.jpg", "link": "https://example-movies.com/movie/joker-2019", "type": "movie"}, {"title": "فيلم الأكشن", "imageUrl": "https://example-movies.com/images/action-movie.jpg", "link": "https://example-movies.com/watch.php?id=123", "type": "movie"}], "series": [], "next_page_links": ["https://example-movies.com/page/2", "https://example-movies.com/more-movies"]}