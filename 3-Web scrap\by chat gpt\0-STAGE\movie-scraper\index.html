<!doctype html>
<html lang="ar">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>واجهة Scraper أفلام</title>
  <style>
    :root{--bg:#0f1724;--card:#111827;--muted:#9ca3af;--accent:#22c55e}
    body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial;background:linear-gradient(180deg,#071023 0%, #071b2b 100%);color:#e6eef8;padding:24px}
    .container{max-width:980px;margin:0 auto}
    .card{background:var(--card);border-radius:12px;padding:18px;box-shadow:0 6px 24px rgba(2,6,23,.6);margin-bottom:16px}
    label{display:block;font-size:14px;margin-bottom:6px;color:#dbeafe}
    input[type=text], input[type=number], input[type=url], textarea, select{width:100%;padding:10px;border-radius:8px;border:1px solid rgba(255,255,255,.06);background:transparent;color:inherit}
    .row{display:flex;gap:12px}
    .col{flex:1}
    .controls{display:flex;gap:8px;flex-wrap:wrap}
    button{background:var(--accent);border:0;padding:10px 14px;border-radius:8px;color:#04201a;font-weight:600;cursor:pointer}
    button.ghost{background:transparent;border:1px solid rgba(255,255,255,.06);color:var(--muted)}
    .small{font-size:13px;color:var(--muted)}
    .switch{display:flex;align-items:center;gap:8px}
    .log{background:#031025;padding:12px;border-radius:8px;height:220px;overflow:auto;font-family:monospace;font-size:13px;color:#9fd3a0}
    table{width:100%;border-collapse:collapse;margin-top:10px}
    th,td{padding:8px;border-bottom:1px solid rgba(255,255,255,.03);text-align:left}
    .actions{display:flex;gap:8px}
    .hint{font-size:13px;color:var(--muted);margin-top:6px}
  </style>
</head>
<body>
  <div class="container">
    <h1 style="margin-bottom:8px">واجهة Scraper — جلب روابط الأفلام</h1>
    <p class="small">صفحة HTML تُمكّنك من تشغيل السكربت (الـ backend مطلوب) أو تصدير الإعدادات كـ JSON. مصممة للعمل مع سكربت بايثون من نوع `scrape`.</p>

    <div class="card">
      <h3>الإعدادات الأساسية</h3>
      <div style="margin-top:8px">
        <label>رابط البداية (start_url)</label>
        <input id="start_url" type="url" value="https://a.asd.homes/main0/" />
      </div>

      <div class="row" style="margin-top:10px">
        <div class="col">
          <label>max_depth</label>
          <input id="max_depth" type="number" min="0" value="2" />
        </div>
        <div class="col">
          <label>delay (ثواني)</label>
          <input id="delay" type="number" min="0" step="0.1" value="1" />
        </div>
        <div class="col">
          <label>نوع المحتوى</label>
          <select id="content_filter">
            <option value="all">الكل (أفلام ومسلسلات)</option>
            <option value="movies">أفلام فقط</option>
            <option value="series">مسلسلات فقط</option>
          </select>
        </div>
      </div>

      <div class="row" style="margin-top:10px">
        <div class="col switch">
          <input id="use_selenium" type="checkbox" /> <label for="use_selenium">استخدم Selenium (لو الموقع يعتمد على JS)</label>
        </div>
        <div class="col switch">
          <input id="ignore_robots" type="checkbox" /> <label for="ignore_robots">تجاهل robots.txt (غير موصى به)</label>
        </div>
      </div>

      <div style="margin-top:12px">
        <label>قواعد استخراج رابط الفيلم (تعبير/كلمة مفتاحية)</label>
        <input id="link_filter" type="text" value="film,movie,.mp4,.mkv,.avi" />
        <div class="hint">مثال: استخدم فواصل لعدة شروط. السكربت سيبحث عن أي شرط ضمن الرابط أو امتداد الملف.</div>
      </div>

    </div>

    <div class="card">
      <h3>حفظ النتائج</h3>
      <div style="margin-top:8px">
        <label>نمط الحفظ</label>
        <select id="save_mode">
          <option value="json">ملف JSON محلي</option>
          <option value="firebase">Firebase Realtime DB</option>
        </select>
      </div>

      <div id="firebase_opts" style="display:none;margin-top:10px">
        <label>Firebase Realtime DB URL</label>
        <input id="firebase_url" type="url" placeholder="https://your-project-id-default-rtdb.firebaseio.com/" />
        <label style="margin-top:8px">ارفع ملف serviceAccountKey.json (اختياري لو تعمل Backend)</label>
        <input id="service_file" type="file" accept="application/json" />
        <div class="hint">في الوضع الأمثل، الـ Backend هو المسؤول عن التعامل مع Firebase بأمان. رفع الملف هنا مفيد إذا كنت تريد أن يرسله العميل للـ backend أو لاستخدامه في بيئة محلية خاصة بك.</div>
      </div>
    </div>

    <div class="card">
      <h3>أزرار التشغيل</h3>
      <div class="controls">
        <button id="startBtn">▶️ ابدأ الزحف</button>
        <button id="stopBtn" class="ghost">⏹ إيقاف</button>
        <button id="getCurrentBtn" class="ghost">📊 تحميل النتائج الحالية</button>
        <button id="exportCfg" class="ghost">💾 تصدير إعدادات (JSON)</button>
        <button id="downloadRes" class="ghost">⬇️ تحميل النتائج النهائية</button>
      </div>
      <div class="hint">ملاحظة: هذه الواجهة تفترض وجود API على نفس السيرفر: <code>/api/scrape</code> لتشغيل السكربت. إذا لم يكن لديك Backend، سيتيح لك الزر "تصدير إعدادات" تنزيل ملف إعدادات لتشغيله محليًا.</div>
    </div>

    <div class="card">
      <h3>السجل (Log)</h3>
      <div id="log" class="log">لا توجد أحداث بعد.</div>
    </div>

    <div class="card">
      <h3>النتائج (مختصر)</h3>
      <div id="resultsSummary" class="small">لم يتم جمع بيانات بعد.</div>
      <table id="resultsTable" style="display:none">
        <thead><tr><th>النوع</th><th>الاسم</th><th>الصورة</th><th>الرابط</th></tr></thead>
        <tbody></tbody>
      </table>
    </div>

    <div class="small" style="margin-top:10px">تم التصميم لسهولة الربط مع السكربتات الخلفية. إذا أردت سأجهز لك مثال Backend (Flask) يتلقى الطلب ويشغّل السكربت ويعيد النتائج.</div>
  </div>

<script>
// عنصر DOM
const startBtn = document.getElementById('startBtn');
const stopBtn = document.getElementById('stopBtn');
const getCurrentBtn = document.getElementById('getCurrentBtn');
const exportCfg = document.getElementById('exportCfg');
const downloadRes = document.getElementById('downloadRes');
const logEl = document.getElementById('log');
const resultsTable = document.getElementById('resultsTable');
const resultsSummary = document.getElementById('resultsSummary');
const firebaseOpts = document.getElementById('firebase_opts');

// التبديل لخيارات Firebase
document.getElementById('save_mode').addEventListener('change', (e)=>{
  firebaseOpts.style.display = e.target.value === 'firebase' ? 'block' : 'none';
});

// وظائف مساعدة
function appendLog(txt){
  const now = new Date().toLocaleTimeString();
  if(logEl.textContent === 'لا توجد أحداث بعد.') logEl.textContent = '';
  logEl.textContent += `[${now}] ${txt}\n`;
  logEl.scrollTop = logEl.scrollHeight;
}

function showResults(data){
  const tbody = resultsTable.querySelector('tbody');
  tbody.innerHTML = '';

  // التحقق من نوع البيانات
  let movies = [];
  let series = [];

  if(data && typeof data === 'object') {
    movies = data.movies_info || [];
    series = data.series_info || [];
  }

  const totalCount = movies.length + series.length;

  if(totalCount === 0){
    resultsSummary.textContent = 'لم يتم العثور على أي محتوى.';
    resultsTable.style.display='none';
    return;
  }

  resultsTable.style.display='table';
  const moviesText = movies.length > 0 ? `${movies.length} فيلم` : '';
  const seriesText = series.length > 0 ? `${series.length} مسلسل` : '';
  const separator = moviesText && seriesText ? ' و ' : '';
  resultsSummary.textContent = `تم العثور على ${moviesText}${separator}${seriesText}.`;

  // عرض الأفلام
  movies.slice(0, 10).forEach(movie => {
    const tr = document.createElement('tr');

    const tdType = document.createElement('td');
    tdType.textContent = '🎬 فيلم';
    tdType.style.color = '#22c55e';

    const tdName = document.createElement('td');
    tdName.textContent = movie.movies_name || 'بدون اسم';

    const tdImage = document.createElement('td');
    if(movie.movies_img) {
      const img = document.createElement('img');
      img.src = movie.movies_img;
      img.style.width = '50px';
      img.style.height = '70px';
      img.style.objectFit = 'cover';
      img.onerror = () => { img.style.display = 'none'; };
      tdImage.appendChild(img);
    } else {
      tdImage.textContent = 'لا توجد صورة';
    }

    const tdLink = document.createElement('td');
    const link = document.createElement('a');
    link.href = movie.movies_href;
    link.textContent = 'رابط الفيلم';
    link.target = '_blank';
    link.style.color = '#22c55e';
    tdLink.appendChild(link);

    tr.appendChild(tdType);
    tr.appendChild(tdName);
    tr.appendChild(tdImage);
    tr.appendChild(tdLink);
    tbody.appendChild(tr);
  });

  // عرض المسلسلات
  series.slice(0, 10).forEach(show => {
    const tr = document.createElement('tr');

    const tdType = document.createElement('td');
    tdType.textContent = '📺 مسلسل';
    tdType.style.color = '#3b82f6';

    const tdName = document.createElement('td');
    tdName.textContent = show.series_name || 'بدون اسم';

    const tdImage = document.createElement('td');
    if(show.series_img) {
      const img = document.createElement('img');
      img.src = show.series_img;
      img.style.width = '50px';
      img.style.height = '70px';
      img.style.objectFit = 'cover';
      img.onerror = () => { img.style.display = 'none'; };
      tdImage.appendChild(img);
    } else {
      tdImage.textContent = 'لا توجد صورة';
    }

    const tdLink = document.createElement('td');
    const link = document.createElement('a');
    link.href = show.series_href;
    link.textContent = 'رابط المسلسل';
    link.target = '_blank';
    link.style.color = '#3b82f6';
    tdLink.appendChild(link);

    tr.appendChild(tdType);
    tr.appendChild(tdName);
    tr.appendChild(tdImage);
    tr.appendChild(tdLink);
    tbody.appendChild(tr);
  });

  // إضافة رسالة إذا كان هناك المزيد
  if(totalCount > 20) {
    const tr = document.createElement('tr');
    const td = document.createElement('td');
    td.colSpan = 4;
    td.textContent = `... و ${totalCount - 20} عنصر إضافي. استخدم "تحميل النتائج" للحصول على القائمة الكاملة.`;
    td.style.textAlign = 'center';
    td.style.fontStyle = 'italic';
    td.style.color = '#9ca3af';
    tr.appendChild(td);
    tbody.appendChild(tr);
  }
}

// حالة مبسطة لتخزين النتائج محليًا
let currentResults = [];
let controller = null;
let currentSessionId = null;
let statusInterval = null;

startBtn.addEventListener('click', async ()=>{
  const cfg = {
    start_url: document.getElementById('start_url').value.trim(),
    max_depth: Number(document.getElementById('max_depth').value),
    delay: Number(document.getElementById('delay').value),
    use_selenium: document.getElementById('use_selenium').checked,
    ignore_robots: document.getElementById('ignore_robots').checked,
    link_filter: document.getElementById('link_filter').value.split(',').map(s=>s.trim()).filter(Boolean),
    save_mode: document.getElementById('save_mode').value,
    content_filter: document.getElementById('content_filter').value
  };

  if(!cfg.start_url) {
    appendLog('⚠️ يرجى إدخال رابط البداية');
    return;
  }

  appendLog('إرسال طلب بدء الزحف...');
  startBtn.disabled = true;
  stopBtn.disabled = false;

  try{
    const res = await fetch('/api/scrape', {
      method: 'POST',
      headers: {'Content-Type':'application/json'},
      body: JSON.stringify(cfg)
    });

    if(!res.ok) throw new Error('خطأ في الاستجابة من السيرفر: '+res.status);

    const data = await res.json();
    currentSessionId = data.session_id;

    appendLog('✅ تم بدء عملية الزحف - معرف الجلسة: ' + currentSessionId.substring(0,8));

    // بدء مراقبة الحالة
    startStatusMonitoring();

  }catch(err){
    appendLog('⛔ خطأ: '+err.message);
    startBtn.disabled = false;
    stopBtn.disabled = true;
  }
});

function startStatusMonitoring() {
  if(statusInterval) clearInterval(statusInterval);

  statusInterval = setInterval(async () => {
    if(!currentSessionId) return;

    try {
      const res = await fetch(`/api/status/${currentSessionId}`);
      const data = await res.json();

      // تحديث السجل بالرسائل الجديدة
      if(data.logs && data.logs.length > 0) {
        data.logs.forEach(log => {
          appendLog(`[${log.time}] ${log.message}`);
        });
      }

      // تحديث شريط التقدم
      if(data.progress !== undefined) {
        // يمكن إضافة شريط تقدم هنا
      }

      // تحديث عدد المحتوى وتحميل النتائج الجزئية
      if(data.total_count > 0) {
        const moviesText = data.movies_count > 0 ? `${data.movies_count} فيلم` : '';
        const seriesText = data.series_count > 0 ? `${data.series_count} مسلسل` : '';
        const separator = moviesText && seriesText ? ' و ' : '';
        resultsSummary.textContent = `تم العثور على ${moviesText}${separator}${seriesText} حتى الآن...`;

        // تحميل النتائج الجزئية كل 20 عنصر
        if(data.total_count > 0 && data.total_count % 20 === 0) {
          try {
            const resultsRes = await fetch(`/api/current-results/${currentSessionId}`);
            const resultsData = await resultsRes.json();
            if(resultsData.data) {
              currentResults = resultsData.data;
              showResults(currentResults);
            }
          } catch(err) {
            // تجاهل الأخطاء في التحديث الجزئي
          }
        }
      }

      // فحص إذا انتهت العملية
      if(data.status === 'completed' || data.status === 'error') {
        clearInterval(statusInterval);
        await loadFinalResults();
        startBtn.disabled = false;
        stopBtn.disabled = true;

        if(data.status === 'completed') {
          appendLog('🎉 انتهت عملية الزحف بنجاح!');
        } else {
          appendLog('❌ انتهت عملية الزحف بخطأ');
        }
      }

    } catch(err) {
      appendLog('⚠️ خطأ في مراقبة الحالة: ' + err.message);
    }
  }, 2000); // فحص كل ثانيتين
}

async function loadFinalResults() {
  if(!currentSessionId) return;

  try {
    const res = await fetch(`/api/results/${currentSessionId}`);
    const data = await res.json();

    currentResults = data.movies || {};
    showResults(currentResults);

  } catch(err) {
    appendLog('⚠️ خطأ في تحميل النتائج: ' + err.message);
  }
}

stopBtn.addEventListener('click', ()=>{
  if(statusInterval) {
    clearInterval(statusInterval);
    statusInterval = null;
  }

  appendLog('تم إيقاف مراقبة العملية');
  startBtn.disabled = false;
  stopBtn.disabled = true;
  currentSessionId = null;
});

getCurrentBtn.addEventListener('click', async ()=>{
  if(!currentSessionId) {
    appendLog('⚠️ لا توجد عملية زحف نشطة');
    return;
  }

  try {
    appendLog('📊 جاري تحميل النتائج الحالية...');
    const res = await fetch(`/api/current-results/${currentSessionId}`);
    const data = await res.json();

    if(data.data && data.count > 0) {
      currentResults = data.data;
      showResults(currentResults);

      const moviesCount = currentResults.movies_info ? currentResults.movies_info.length : 0;
      const seriesCount = currentResults.series_info ? currentResults.series_info.length : 0;
      appendLog(`✅ تم تحميل ${moviesCount} فيلم و ${seriesCount} مسلسل`);

      // تحميل الملف مباشرة
      const blob = new Blob([JSON.stringify(currentResults,null,2)], {type:'application/json'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href=url;
      a.download=`content_current_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.json`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      appendLog(`📁 تم تنزيل ${data.count} عنصر في ملف JSON`);
    } else {
      appendLog('⚠️ لا توجد نتائج حتى الآن');
    }
  } catch(err) {
    appendLog('❌ خطأ في تحميل النتائج: ' + err.message);
  }
});

exportCfg.addEventListener('click', ()=>{
  const cfg = {
    start_url: document.getElementById('start_url').value.trim(),
    max_depth: Number(document.getElementById('max_depth').value),
    delay: Number(document.getElementById('delay').value),
    use_selenium: document.getElementById('use_selenium').checked,
    ignore_robots: document.getElementById('ignore_robots').checked,
    link_filter: document.getElementById('link_filter').value.split(',').map(s=>s.trim()).filter(Boolean),
    content_filter: document.getElementById('content_filter').value
  };
  const blob = new Blob([JSON.stringify(cfg,null,2)], {type:'application/json'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a'); a.href=url; a.download='scrape-config.json'; document.body.appendChild(a); a.click(); a.remove();
  appendLog('تم تصدير إعدادات scrape-config.json');
});

downloadRes.addEventListener('click', async ()=>{
  // محاولة تحميل النتائج الحالية من الخادم أولاً
  if(currentSessionId) {
    try {
      const res = await fetch(`/api/current-results/${currentSessionId}`);
      const data = await res.json();
      if(data.data && data.count > 0) {
        currentResults = data.data;
        appendLog(`تم تحديث النتائج: ${data.count} عنصر`);
      }
    } catch(err) {
      appendLog('⚠️ خطأ في تحميل النتائج من الخادم: ' + err.message);
    }
  }

  // التحقق من وجود نتائج
  let hasResults = false;
  if(currentResults && typeof currentResults === 'object') {
    const moviesCount = currentResults.movies_info ? currentResults.movies_info.length : 0;
    const seriesCount = currentResults.series_info ? currentResults.series_info.length : 0;
    hasResults = moviesCount > 0 || seriesCount > 0;
  }

  if(!hasResults){
    appendLog('لا توجد نتائج للتحميل.');
    return;
  }

  const blob = new Blob([JSON.stringify(currentResults,null,2)], {type:'application/json'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href=url;
  a.download=`content_final_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.json`;
  document.body.appendChild(a);
  a.click();
  a.remove();

  const moviesCount = currentResults.movies_info ? currentResults.movies_info.length : 0;
  const seriesCount = currentResults.series_info ? currentResults.series_info.length : 0;
  appendLog(`تم تنزيل ${moviesCount} فيلم و ${seriesCount} مسلسل في ملف JSON`);
});

// لو لم يتوفر Backend، يمكنك استخدام زر "تصدير إعدادات" ثم تشغيل السكربت محليًا بقراءة ملف JSON

appendLog('الواجهة جاهزة.');
</script>
</body>
</html>
