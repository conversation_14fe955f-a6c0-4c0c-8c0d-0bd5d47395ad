from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
from collections import deque
import threading
import uuid
from datetime import datetime
import json
import os
import re
from typing import List, Dict, Optional, Tuple

app = Flask(__name__)
CORS(app)  # تطبيق CORS على التطبيق

# متغيرات عامة لتتبع حالة العمليات
scraping_sessions = {}  # {session_id: {status, progress, logs, movies, thread}}
session_lock = threading.Lock()

def add_log(session_id, message, log_type="info"):
    """إضافة رسالة إلى سجل الجلسة"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    log_entry = {
        "time": timestamp,
        "message": message,
        "type": log_type
    }

    with session_lock:
        if session_id in scraping_sessions:
            scraping_sessions[session_id]["logs"].append(log_entry)
            print(f"[{timestamp}] {message}")  # طباعة في سجل الخادم أيضاً

def update_progress(session_id, progress, status=None):
    """تحديث تقدم العملية"""
    with session_lock:
        if session_id in scraping_sessions:
            scraping_sessions[session_id]["progress"] = progress
            if status:
                scraping_sessions[session_id]["status"] = status

# قواعد استخراج متعددة للمواقع المختلفة
class ExtractionRules:
    """فئة تحتوي على قواعد استخراج مختلفة للمواقع"""

    @staticmethod
    def get_all_rules():
        """إرجاع جميع قواعد الاستخراج المتاحة"""
        return [
            ExtractionRules.rule_generic_movie_cards,
            ExtractionRules.rule_arabic_movie_sites,
            ExtractionRules.rule_wordpress_themes,
            ExtractionRules.rule_bootstrap_cards,
            ExtractionRules.rule_flex_grid_layouts,
            ExtractionRules.rule_list_based_layouts,
            ExtractionRules.rule_table_based_layouts,
            ExtractionRules.rule_custom_movie_sites
        ]

    @staticmethod
    def rule_generic_movie_cards(soup, base_url):
        """قاعدة عامة لبطاقات الأفلام"""
        items = []

        # البحث عن بطاقات الأفلام بالكلاسات الشائعة
        selectors = [
            '.movie-card', '.film-card', '.content-card', '.post-card',
            '.movie-item', '.film-item', '.content-item', '.post-item',
            '.movie', '.film', '.content', '.post',
            '[class*="movie"]', '[class*="film"]', '[class*="card"]'
        ]

        for selector in selectors:
            cards = soup.select(selector)
            for card in cards:
                item = ExtractionRules._extract_from_card(card, base_url)
                if item:
                    items.append(item)

        return items

    @staticmethod
    def rule_arabic_movie_sites(soup, base_url):
        """قاعدة خاصة بالمواقع العربية للأفلام"""
        items = []

        # البحث عن العناصر بالكلاسات العربية الشائعة
        arabic_selectors = [
            '.فيلم', '.مسلسل', '.حلقة', '.موسم',
            '[class*="فيلم"]', '[class*="مسلسل"]',
            '.movie-arabic', '.film-arabic', '.arabic-content'
        ]

        for selector in arabic_selectors:
            cards = soup.select(selector)
            for card in cards:
                item = ExtractionRules._extract_from_card(card, base_url)
                if item:
                    items.append(item)

        return items

    @staticmethod
    def rule_wordpress_themes(soup, base_url):
        """قاعدة لمواقع WordPress"""
        items = []

        # البحث عن عناصر WordPress الشائعة
        wp_selectors = [
            '.post', '.entry', '.article', '.hentry',
            '.post-item', '.entry-item', '.article-item',
            '.wp-post', '.wp-entry'
        ]

        for selector in wp_selectors:
            cards = soup.select(selector)
            for card in cards:
                item = ExtractionRules._extract_from_card(card, base_url)
                if item:
                    items.append(item)

        return items

    @staticmethod
    def rule_bootstrap_cards(soup, base_url):
        """قاعدة لبطاقات Bootstrap"""
        items = []

        # البحث عن بطاقات Bootstrap
        bootstrap_selectors = [
            '.card', '.card-body', '.media', '.media-object',
            '.thumbnail', '.panel', '.well'
        ]

        for selector in bootstrap_selectors:
            cards = soup.select(selector)
            for card in cards:
                item = ExtractionRules._extract_from_card(card, base_url)
                if item:
                    items.append(item)

        return items

    @staticmethod
    def rule_flex_grid_layouts(soup, base_url):
        """قاعدة للتخطيطات المرنة والشبكية"""
        items = []

        # البحث عن عناصر الشبكة المرنة
        flex_selectors = [
            '.grid-item', '.flex-item', '.col', '.column',
            '[class*="col-"]', '[class*="grid-"]', '[class*="flex-"]'
        ]

        for selector in flex_selectors:
            cards = soup.select(selector)
            for card in cards:
                item = ExtractionRules._extract_from_card(card, base_url)
                if item:
                    items.append(item)

        return items

    @staticmethod
    def rule_list_based_layouts(soup, base_url):
        """قاعدة للتخطيطات القائمة على القوائم"""
        items = []

        # البحث في عناصر القوائم
        list_items = soup.select('li')
        for li in list_items:
            item = ExtractionRules._extract_from_card(li, base_url)
            if item:
                items.append(item)

        return items

    @staticmethod
    def rule_table_based_layouts(soup, base_url):
        """قاعدة للتخطيطات القائمة على الجداول"""
        items = []

        # البحث في صفوف الجداول
        table_rows = soup.select('tr')
        for tr in table_rows:
            item = ExtractionRules._extract_from_card(tr, base_url)
            if item:
                items.append(item)

        return items

    @staticmethod
    def rule_custom_movie_sites(soup, base_url):
        """قاعدة مخصصة للمواقع المعروفة"""
        items = []

        # قواعد خاصة بمواقع معينة
        domain = urlparse(base_url).netloc.lower()

        if 'cimanow' in domain:
            # قاعدة خاصة بموقع cimanow
            cards = soup.select('.post, .movie-item, .film-item')
            for card in cards:
                item = ExtractionRules._extract_from_card(card, base_url)
                if item:
                    items.append(item)

        elif 'asd.homes' in domain:
            # قاعدة خاصة بموقع asd.homes
            cards = soup.select('.entry, .post-item, .content-item')
            for card in cards:
                item = ExtractionRules._extract_from_card(card, base_url)
                if item:
                    items.append(item)

        return items

    @staticmethod
    def _extract_from_card(card, base_url):
        """استخراج معلومات من بطاقة واحدة"""
        try:
            # البحث عن الرابط
            link_elem = card.find('a', href=True)
            if not link_elem:
                return None

            href = urljoin(base_url, link_elem.get('href', ''))

            # تخطي الروابط غير المرغوبة
            skip_patterns = [
                'javascript:', 'mailto:', '#', 'login', 'register',
                'admin', 'search', 'contact', 'about'
            ]

            if any(pattern in href.lower() for pattern in skip_patterns):
                return None

            # استخراج العنوان
            title = ExtractionRules._extract_title(card, link_elem)
            if not title or len(title.strip()) < 3:
                return None

            # استخراج الصورة
            img_url = ExtractionRules._extract_image(card, base_url)

            # تحديد نوع المحتوى
            content_type = ExtractionRules._determine_content_type(title, href)

            # التحقق من أن هذا محتوى صالح
            if ExtractionRules._is_valid_content(title, href, img_url):
                return {
                    "title": title.strip(),
                    "imageUrl": img_url,
                    "link": href,
                    "type": content_type
                }

        except Exception:
            pass

        return None

    @staticmethod
    def _extract_title(card, link_elem):
        """استخراج العنوان من البطاقة"""
        title = ""

        # 1. من alt أو title الصورة
        img = card.find('img')
        if img:
            title = img.get('alt', '') or img.get('title', '')

        # 2. من نص الرابط
        if not title:
            title = link_elem.get_text().strip()

        # 3. من العناوين
        if not title:
            for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                title_elem = card.find(tag)
                if title_elem:
                    title = title_elem.get_text().strip()
                    break

        # 4. من العناصر بكلاسات العنوان
        if not title:
            title_selectors = [
                '.title', '.name', '.heading', '.header',
                '[class*="title"]', '[class*="name"]', '[class*="heading"]'
            ]
            for selector in title_selectors:
                title_elem = card.select_one(selector)
                if title_elem:
                    title = title_elem.get_text().strip()
                    break

        # 5. من title أو data-title
        if not title:
            title = card.get('title', '') or card.get('data-title', '')

        # تنظيف العنوان
        if title:
            title = re.sub(r'\s+', ' ', title).strip()
            # إزالة النصوص الشائعة غير المرغوبة
            unwanted_texts = ['مشاهدة', 'تحميل', 'watch', 'download', 'online', 'free']
            for unwanted in unwanted_texts:
                title = re.sub(rf'\b{unwanted}\b', '', title, flags=re.IGNORECASE).strip()

        return title

    @staticmethod
    def _extract_image(card, base_url):
        """استخراج رابط الصورة من البطاقة"""
        img_url = ""

        # البحث عن الصورة
        img = card.find('img')
        if img:
            # جرب مصادر مختلفة للصورة
            img_sources = ['src', 'data-src', 'data-lazy-src', 'data-original', 'data-srcset']
            for source in img_sources:
                img_url = img.get(source, '')
                if img_url:
                    break

            if img_url:
                img_url = urljoin(base_url, img_url)

        # إذا لم نجد صورة، ابحث عن صورة خلفية في CSS
        if not img_url:
            style = card.get('style', '')
            if 'background-image' in style:
                match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
                if match:
                    img_url = urljoin(base_url, match.group(1))

        # صورة افتراضية إذا لم نجد شيء
        if not img_url:
            img_url = "https://via.placeholder.com/300x400?text=Movie"

        return img_url

    @staticmethod
    def _determine_content_type(title, href):
        """تحديد نوع المحتوى (فيلم أم مسلسل)"""
        title_lower = title.lower()
        href_lower = href.lower()

        # كلمات مفتاحية للمسلسلات
        series_keywords = [
            'series', 'season', 'episode', 'مسلسل', 'موسم', 'حلقة', 'مسلسلات',
            'الموسم', 'الحلقة', 'مدبلج', 'مترجم', 'tv', 'show'
        ]

        if any(keyword in title_lower or keyword in href_lower for keyword in series_keywords):
            return "series"

        return "movie"

    @staticmethod
    def _is_valid_content(title, href, img_url):
        """التحقق من صحة المحتوى"""
        # التحقق من وجود كلمات مفتاحية للمحتوى
        content_keywords = [
            'watch', 'movie', 'film', 'series', 'فيلم', 'مسلسل', 'مشاهدة',
            'episode', 'season', 'حلقة', 'موسم'
        ]

        title_lower = title.lower()
        href_lower = href.lower()

        # يجب أن يحتوي العنوان أو الرابط على كلمة مفتاحية
        has_keywords = any(keyword in title_lower or keyword in href_lower for keyword in content_keywords)

        # أو يجب أن يكون الرابط يحتوي على معاملات مشاهدة
        has_watch_params = any(param in href_lower for param in ['watch.php', 'view.php', 'play.php', 'movie.php'])

        return has_keywords or has_watch_params

def extract_content_info(soup, url):
    """استخراج معلومات المحتوى باستخدام قواعد متعددة مع تقييم الجودة"""
    rules = ExtractionRules.get_all_rules()
    rule_results = []

    # تجريب كل قاعدة وتقييم النتائج
    for rule in rules:
        try:
            items = rule(soup, url)
            if items:
                quality_score = _evaluate_extraction_quality(items, soup, url)
                rule_results.append({
                    'rule': rule.__name__,
                    'items': items,
                    'quality_score': quality_score,
                    'count': len(items)
                })
        except Exception as e:
            continue

    # ترتيب القواعد حسب الجودة
    rule_results.sort(key=lambda x: x['quality_score'], reverse=True)

    # دمج النتائج من أفضل القواعد
    all_items = []
    used_rules = []

    for result in rule_results:
        if result['quality_score'] > 0.3:  # حد أدنى للجودة
            all_items.extend(result['items'])
            used_rules.append(result['rule'])

    # إذا لم نحصل على نتائج جيدة، استخدم جميع النتائج
    if not all_items:
        for result in rule_results:
            all_items.extend(result['items'])
            used_rules.append(result['rule'])

    # إزالة المكررات بناءً على الرابط
    seen_links = set()
    unique_items = []
    for item in all_items:
        if item["link"] not in seen_links:
            seen_links.add(item["link"])
            unique_items.append(item)

    return unique_items

def _evaluate_extraction_quality(items, soup, url):
    """تقييم جودة الاستخراج"""
    if not items:
        return 0.0

    quality_score = 0.0
    total_items = len(items)

    # معايير التقييم
    valid_titles = 0
    valid_images = 0
    valid_links = 0
    unique_titles = set()

    for item in items:
        # تقييم العنوان
        title = item.get('title', '').strip()
        if title and len(title) >= 3:
            valid_titles += 1
            unique_titles.add(title.lower())

            # نقاط إضافية للعناوين التي تحتوي على كلمات مفتاحية
            movie_keywords = ['movie', 'film', 'فيلم', 'مسلسل', 'series']
            if any(keyword in title.lower() for keyword in movie_keywords):
                quality_score += 0.1

        # تقييم الصورة
        img_url = item.get('imageUrl', '')
        if img_url and img_url != "https://via.placeholder.com/300x400?text=Movie":
            valid_images += 1

        # تقييم الرابط
        link = item.get('link', '')
        if link and link != url:
            valid_links += 1

            # نقاط إضافية للروابط التي تحتوي على معاملات مشاهدة
            watch_params = ['watch.php', 'view.php', 'play.php', 'movie.php']
            if any(param in link.lower() for param in watch_params):
                quality_score += 0.1

    # حساب النسب
    title_ratio = valid_titles / total_items if total_items > 0 else 0
    image_ratio = valid_images / total_items if total_items > 0 else 0
    link_ratio = valid_links / total_items if total_items > 0 else 0
    uniqueness_ratio = len(unique_titles) / total_items if total_items > 0 else 0

    # حساب النتيجة النهائية
    quality_score += (title_ratio * 0.3 +
                     image_ratio * 0.2 +
                     link_ratio * 0.3 +
                     uniqueness_ratio * 0.2)

    # نقاط إضافية للكمية المعقولة
    if 5 <= total_items <= 50:
        quality_score += 0.1
    elif total_items > 50:
        quality_score += 0.05

    return min(quality_score, 1.0)  # الحد الأقصى 1.0

def extract_content_info_with_details(soup, url):
    """استخراج معلومات المحتوى مع إرجاع تفاصيل العملية"""
    rules = ExtractionRules.get_all_rules()
    rule_results = []

    # تجريب كل قاعدة وتقييم النتائج
    for rule in rules:
        try:
            items = rule(soup, url)
            if items:
                quality_score = _evaluate_extraction_quality(items, soup, url)
                rule_results.append({
                    'rule': rule.__name__,
                    'items': items,
                    'quality_score': quality_score,
                    'count': len(items)
                })
        except Exception as e:
            continue

    # ترتيب القواعد حسب الجودة
    rule_results.sort(key=lambda x: x['quality_score'], reverse=True)

    # دمج النتائج من أفضل القواعد
    all_items = []
    used_rules = []
    best_rule = None
    best_score = 0.0

    for result in rule_results:
        if result['quality_score'] > 0.3:  # حد أدنى للجودة
            all_items.extend(result['items'])
            used_rules.append(result['rule'])

            if not best_rule:
                best_rule = result['rule']
                best_score = result['quality_score']

    # إذا لم نحصل على نتائج جيدة، استخدم جميع النتائج
    if not all_items and rule_results:
        for result in rule_results:
            all_items.extend(result['items'])
            used_rules.append(result['rule'])

        best_rule = rule_results[0]['rule']
        best_score = rule_results[0]['quality_score']

    # إزالة المكررات بناءً على الرابط
    seen_links = set()
    unique_items = []
    for item in all_items:
        if item["link"] not in seen_links:
            seen_links.add(item["link"])
            unique_items.append(item)

    # معلومات الاستخراج
    extraction_info = {
        'best_rule': best_rule,
        'best_score': best_score,
        'used_rules': used_rules,
        'total_rules_tested': len(rule_results),
        'rule_results': rule_results
    }

    return unique_items, extraction_info

def find_next_page_links(soup, current_url):
    """البحث عن روابط الصفحات التالية تلقائياً"""
    next_links = []
    domain = urlparse(current_url).netloc

    # قواعد مختلفة للعثور على روابط الصفحات التالية
    pagination_strategies = [
        _find_pagination_by_text,
        _find_pagination_by_class,
        _find_pagination_by_numbers,
        _find_pagination_by_arrows,
        _find_pagination_by_params
    ]

    for strategy in pagination_strategies:
        try:
            links = strategy(soup, current_url, domain)
            if links:
                next_links.extend(links)
        except Exception:
            continue

    # إزالة المكررات والروابط غير الصالحة
    unique_links = []
    seen_links = set()

    for link in next_links:
        if (link not in seen_links and
            link != current_url and
            domain in urlparse(link).netloc):
            seen_links.add(link)
            unique_links.append(link)

    return unique_links

def _find_pagination_by_text(soup, current_url, domain):
    """البحث عن الصفحات التالية بالنص"""
    links = []

    # كلمات مفتاحية للصفحة التالية
    next_keywords = [
        'next', 'التالي', 'التالية', 'المزيد', 'more', 'continue',
        'صفحة تالية', 'الصفحة التالية', 'next page', 'more posts',
        '»', '>', '→', 'التالى', 'المزيد من الأفلام'
    ]

    # البحث في جميع الروابط
    for a in soup.find_all('a', href=True):
        text = a.get_text().strip().lower()
        title = a.get('title', '').lower()

        if any(keyword.lower() in text or keyword.lower() in title for keyword in next_keywords):
            href = urljoin(current_url, a['href'])
            if domain in urlparse(href).netloc:
                links.append(href)

    return links

def _find_pagination_by_class(soup, current_url, domain):
    """البحث عن الصفحات التالية بالكلاسات"""
    links = []

    # كلاسات شائعة للصفحات التالية
    pagination_classes = [
        'next', 'next-page', 'pagination-next', 'page-next',
        'btn-next', 'link-next', 'nav-next', 'more-posts',
        'load-more', 'show-more', 'التالي', 'المزيد'
    ]

    for class_name in pagination_classes:
        # البحث بالكلاس الدقيق
        elements = soup.find_all(attrs={'class': lambda x: x and class_name in ' '.join(x).lower()})

        for elem in elements:
            # إذا كان العنصر رابط
            if elem.name == 'a' and elem.get('href'):
                href = urljoin(current_url, elem['href'])
                if domain in urlparse(href).netloc:
                    links.append(href)

            # إذا كان العنصر يحتوي على رابط
            else:
                a = elem.find('a', href=True)
                if a:
                    href = urljoin(current_url, a['href'])
                    if domain in urlparse(href).netloc:
                        links.append(href)

    return links

def _find_pagination_by_numbers(soup, current_url, domain):
    """البحث عن الصفحات التالية بالأرقام"""
    links = []

    # البحث عن أرقام الصفحات
    for a in soup.find_all('a', href=True):
        text = a.get_text().strip()

        # إذا كان النص رقم
        if text.isdigit():
            href = urljoin(current_url, a['href'])
            if domain in urlparse(href).netloc:
                # التحقق من أن الرابط يحتوي على معاملات صفحة
                if any(param in href.lower() for param in ['page=', 'p=', 'offset=', 'start=']):
                    links.append(href)

    return links

def _find_pagination_by_arrows(soup, current_url, domain):
    """البحث عن الصفحات التالية بالأسهم والرموز"""
    links = []

    # رموز الأسهم والتنقل
    arrow_symbols = ['»', '>', '→', '▶', '⏭', '⏩', '➡', '⇒', '⇨']

    for a in soup.find_all('a', href=True):
        text = a.get_text().strip()

        if any(symbol in text for symbol in arrow_symbols):
            href = urljoin(current_url, a['href'])
            if domain in urlparse(href).netloc:
                links.append(href)

    return links

def _find_pagination_by_params(soup, current_url, domain):
    """البحث عن الصفحات التالية بمعاملات URL"""
    links = []

    # البحث عن روابط تحتوي على معاملات صفحة
    for a in soup.find_all('a', href=True):
        href = urljoin(current_url, a['href'])

        if domain in urlparse(href).netloc:
            # التحقق من وجود معاملات الصفحة
            page_params = ['page=', 'p=', 'offset=', 'start=', 'paged=']

            if any(param in href.lower() for param in page_params):
                # استخراج رقم الصفحة
                for param in page_params:
                    if param in href.lower():
                        try:
                            # استخراج الرقم بعد المعامل
                            match = re.search(rf'{param}(\d+)', href.lower())
                            if match:
                                page_num = int(match.group(1))
                                # إضافة الصفحات القريبة فقط
                                if page_num <= 50:  # حد أقصى للصفحات
                                    links.append(href)
                                break
                        except:
                            continue

    return links

def scrape_worker(session_id, start_url, max_depth, delay, link_filters):
    """دالة الزحف التي تعمل في خيط منفصل"""
    try:
        add_log(session_id, f"🚀 بدء الزحف من: {start_url}")
        add_log(session_id, f"📊 العمق الأقصى: {max_depth}, التأخير: {delay} ثانية")
        add_log(session_id, f"🔍 الفلاتر: {link_filters}")

        visited = set()
        movies_info = []
        series_info = []
        queue = deque([(start_url, 0)])
        domain = urlparse(start_url).netloc

        add_log(session_id, f"🌐 النطاق المستهدف: {domain}")
        update_progress(session_id, 5, "running")

        total_processed = 0
        while queue:
            url, depth = queue.popleft()

            if url in visited or depth > max_depth:
                continue

            visited.add(url)
            total_processed += 1

            add_log(session_id, f"📄 معالجة الصفحة {total_processed}: {url[:50]}...")
            update_progress(session_id, min(10 + (total_processed * 2), 90))

            try:
                add_log(session_id, f"⬇️ تحميل الصفحة...")
                r = requests.get(url, timeout=15)

                if "text/html" not in r.headers.get("Content-Type", ""):
                    add_log(session_id, f"⚠️ تخطي - ليس HTML")
                    continue

                add_log(session_id, f"✅ تم التحميل ({len(r.text)} حرف)")
                soup = BeautifulSoup(r.text, "html.parser")

                # استخراج معلومات المحتوى
                add_log(session_id, f"🔍 استخراج معلومات المحتوى...")

                # إحصائيات تشخيصية
                all_links = soup.find_all("a", href=True)
                links_with_images = [link for link in all_links if link.find('img')]
                add_log(session_id, f"📊 إجمالي الروابط: {len(all_links)}, روابط بصور: {len(links_with_images)}")

                content_items, extraction_info = extract_content_info_with_details(soup, url)
                add_log(session_id, f"📋 تم استخراج {len(content_items)} عنصر من الصفحة")

                # تسجيل معلومات القواعد المستخدمة
                if extraction_info:
                    add_log(session_id, f"🎯 أفضل قاعدة: {extraction_info['best_rule']} (جودة: {extraction_info['best_score']:.2f})")
                    add_log(session_id, f"🔧 قواعد مستخدمة: {', '.join(extraction_info['used_rules'])}")

                movies_found = 0
                series_found = 0
                links_added = 0

                # معالجة العناصر المستخرجة
                for item in content_items:
                    # فلترة على أساس domain أولاً
                    if domain not in urlparse(item["link"]).netloc:
                        continue

                    # فحص الفلاتر (إذا كانت موجودة)
                    if link_filters:
                        filter_match = False
                        for f in link_filters:
                            if (f in item["link"].lower() or
                                f in item["title"].lower() or
                                f in item["imageUrl"].lower()):
                                filter_match = True
                                break
                        if not filter_match:
                            continue
                        if item["type"] == "series":
                            series_info.append({
                                "series_name": item["title"],
                                "series_img": item["imageUrl"],
                                "series_href": item["link"]
                            })
                            series_found += 1
                        else:
                            movies_info.append({
                                "movies_name": item["title"],
                                "movies_img": item["imageUrl"],
                                "movies_href": item["link"]
                            })
                            movies_found += 1

                        # إضافة للطابور للزحف العميق
                        if item["link"] not in visited and depth < max_depth:
                            queue.append((item["link"], depth + 1))
                            links_added += 1

                # البحث عن روابط الصفحات التالية باستخدام النظام الذكي
                add_log(session_id, f"🔍 البحث عن روابط الصفحات التالية...")
                next_page_links = find_next_page_links(soup, url)

                for next_link in next_page_links:
                    if next_link not in visited and depth < max_depth:
                        queue.append((next_link, depth + 1))
                        links_added += 1

                add_log(session_id, f"🔗 وجد {len(next_page_links)} رابط للصفحات التالية")

                # البحث عن روابط الفئات والأقسام
                category_links = soup.find_all("a", href=True)
                for a in category_links:
                    href = urljoin(url, a["href"])
                    if domain in urlparse(href).netloc and href not in visited and depth < max_depth:
                        link_text = a.get_text().strip().lower()
                        href_lower = href.lower()

                        # إضافة روابط الفئات والأقسام
                        category_keywords = [
                            'category', 'فئة', 'قسم', 'أفلام', 'مسلسلات', 'movies', 'series',
                            'genre', 'نوع', 'تصنيف', 'foreign', 'arabic', 'عربي', 'أجنبي'
                        ]

                        # أو إذا كان الرابط يحتوي على معاملات فئات
                        has_category = any(param in href_lower for param in ['category', 'cat=', 'genre=', 'type='])
                        has_keywords = any(word in link_text for word in category_keywords)

                        if has_category or has_keywords:
                            queue.append((href, depth + 1))
                            links_added += 1

                add_log(session_id, f"🎬 وجد {movies_found} فيلم جديد")
                add_log(session_id, f"📺 وجد {series_found} مسلسل جديد")
                add_log(session_id, f"➕ أضيف {links_added} رابط للطابور")

                # تحديث النتائج في الجلسة
                results_data = {
                    "movies_info": movies_info,
                    "series_info": series_info
                }

                with session_lock:
                    if session_id in scraping_sessions:
                        scraping_sessions[session_id]["movies"] = results_data

                # حفظ دوري كل 50 عنصر
                total_items = len(movies_info) + len(series_info)
                if total_items > 0 and total_items % 50 == 0:
                    try:
                        temp_filename = f"content_temp_{session_id[:8]}.json"
                        with open(temp_filename, 'w', encoding='utf-8') as f:
                            json.dump(results_data, f, ensure_ascii=False, indent=2)
                        add_log(session_id, f"💾 حفظ مؤقت: {len(movies_info)} فيلم، {len(series_info)} مسلسل")
                    except Exception as e:
                        add_log(session_id, f"⚠️ خطأ في الحفظ المؤقت: {str(e)}", "error")

            except Exception as e:
                add_log(session_id, f"⚠️ خطأ في تحميل {url[:30]}...: {str(e)}", "error")
                continue

            add_log(session_id, f"⏳ انتظار {delay} ثانية...")
            time.sleep(delay)

        # انتهاء العملية
        total_movies = len(movies_info)
        total_series = len(series_info)
        add_log(session_id, f"🎉 انتهت العملية! وجد {total_movies} فيلم و {total_series} مسلسل")
        update_progress(session_id, 100, "completed")

        # إعداد البيانات النهائية
        final_results = {
            "movies_info": movies_info,
            "series_info": series_info
        }

        # حفظ النتائج في ملف
        try:
            filename = f"content_{session_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(final_results, f, ensure_ascii=False, indent=2)
            add_log(session_id, f"💾 تم حفظ النتائج في ملف: {filename}")
        except Exception as e:
            add_log(session_id, f"⚠️ خطأ في حفظ الملف: {str(e)}", "error")

        with session_lock:
            if session_id in scraping_sessions:
                scraping_sessions[session_id]["movies"] = final_results

    except Exception as e:
        add_log(session_id, f"❌ خطأ عام: {str(e)}", "error")
        update_progress(session_id, 0, "error")

# صفحة HTML لو فتحت السيرفر مباشرة
@app.route("/")
def home():
    # خدمة ملف index.html مباشرة
    with open('index.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route("/api/scrape", methods=["POST"])
def api_scrape():
    """بدء عملية زحف جديدة"""
    cfg = request.json
    start_url = cfg.get("start_url")
    max_depth = int(cfg.get("max_depth", 2))
    delay = float(cfg.get("delay", 1))

    # معالجة link_filter
    link_filter_raw = cfg.get("link_filter", "")
    if isinstance(link_filter_raw, list):
        link_filters = [f.lower().strip() for f in link_filter_raw if f.strip()]
    else:
        link_filters = [f.lower().strip() for f in str(link_filter_raw).split(',') if f.strip()]

    if not start_url:
        return jsonify({"error": "❌ start_url مطلوب"}), 400

    # إنشاء جلسة جديدة
    session_id = str(uuid.uuid4())

    with session_lock:
        scraping_sessions[session_id] = {
            "status": "starting",
            "progress": 0,
            "logs": [],
            "movies": [],
            "thread": None
        }

    # بدء الزحف في خيط منفصل
    thread = threading.Thread(
        target=scrape_worker,
        args=(session_id, start_url, max_depth, delay, link_filters)
    )
    thread.daemon = True
    thread.start()

    with session_lock:
        scraping_sessions[session_id]["thread"] = thread

    return jsonify({
        "session_id": session_id,
        "message": "تم بدء عملية الزحف",
        "status": "started"
    })

@app.route("/api/status/<session_id>", methods=["GET"])
def get_status(session_id):
    """الحصول على حالة عملية الزحف"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        movies_data = session["movies"]

        # حساب عدد العناصر
        movies_count = 0
        series_count = 0
        if isinstance(movies_data, dict):
            movies_count = len(movies_data.get("movies_info", []))
            series_count = len(movies_data.get("series_info", []))

        return jsonify({
            "status": session["status"],
            "progress": session["progress"],
            "logs": session["logs"][-10:],  # آخر 10 رسائل
            "movies_count": movies_count,
            "series_count": series_count,
            "total_count": movies_count + series_count
        })

@app.route("/api/results/<session_id>", methods=["GET"])
def get_results(session_id):
    """الحصول على نتائج الزحف"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        return jsonify({
            "status": session["status"],
            "progress": session["progress"],
            "movies": session["movies"]
        })

@app.route("/api/logs/<session_id>", methods=["GET"])
def get_logs(session_id):
    """الحصول على جميع السجلات"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        return jsonify({
            "logs": session["logs"],
            "status": session["status"],
            "progress": session["progress"]
        })

@app.route("/api/current-results/<session_id>", methods=["GET"])
def get_current_results(session_id):
    """الحصول على النتائج الحالية حتى لو لم تكتمل العملية"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        movies_data = session["movies"]

        # حساب العدد الإجمالي
        total_count = 0
        if isinstance(movies_data, dict):
            movies_count = len(movies_data.get("movies_info", []))
            series_count = len(movies_data.get("series_info", []))
            total_count = movies_count + series_count

        return jsonify({
            "data": movies_data,
            "count": total_count,
            "status": session["status"],
            "progress": session["progress"]
        })

if __name__ == "__main__":
    app.run(debug=True)
