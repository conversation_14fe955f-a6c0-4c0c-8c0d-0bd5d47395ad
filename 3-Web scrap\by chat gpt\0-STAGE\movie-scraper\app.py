from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
from collections import deque
import threading
import uuid
from datetime import datetime
import json
import os

app = Flask(__name__)
CORS(app)  # تطبيق CORS على التطبيق

# متغيرات عامة لتتبع حالة العمليات
scraping_sessions = {}  # {session_id: {status, progress, logs, movies, thread}}
session_lock = threading.Lock()

def add_log(session_id, message, log_type="info"):
    """إضافة رسالة إلى سجل الجلسة"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    log_entry = {
        "time": timestamp,
        "message": message,
        "type": log_type
    }

    with session_lock:
        if session_id in scraping_sessions:
            scraping_sessions[session_id]["logs"].append(log_entry)
            print(f"[{timestamp}] {message}")  # طباعة في سجل الخادم أيضاً

def update_progress(session_id, progress, status=None):
    """تحديث تقدم العملية"""
    with session_lock:
        if session_id in scraping_sessions:
            scraping_sessions[session_id]["progress"] = progress
            if status:
                scraping_sessions[session_id]["status"] = status

def extract_content_info(soup, url):
    """استخراج معلومات المحتوى (اسم، صورة، رابط)"""
    content_items = []

    # أولاً: البحث عن جميع الروابط التي تحتوي على صور
    all_links = soup.find_all('a', href=True)

    for link in all_links:
        try:
            href = urljoin(url, link.get('href', ''))

            # تخطي الروابط الداخلية والإدارية
            skip_patterns = [
                'javascript:', 'mailto:', '#',
                'login', 'register', 'admin', 'search'
            ]

            if any(pattern in href.lower() for pattern in skip_patterns):
                continue

            # البحث عن صورة داخل الرابط أو في العنصر الأب
            img = link.find('img')
            if not img and link.parent:
                img = link.parent.find('img')

            # إذا لم نجد صورة، تحقق من أن الرابط يحتوي على كلمات مفتاحية للأفلام
            has_movie_keywords = False
            if not img:
                movie_keywords = ['watch.php', 'movie', 'film', 'فيلم', 'مشاهدة']
                if any(keyword in href.lower() for keyword in movie_keywords):
                    has_movie_keywords = True
                else:
                    continue

            # استخراج اسم المحتوى
            title = ""

            # جرب استخراج العنوان من عدة مصادر
            # 1. من alt أو title الصورة
            if img:
                title = img.get('alt', '') or img.get('title', '')

            # 2. من نص الرابط
            if not title:
                title = link.get_text().strip()

            # 3. من العناصر المجاورة
            if not title:
                parent = link.parent
                if parent:
                    # ابحث عن عناوين في العنصر الأب
                    for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                        title_elem = parent.find(tag)
                        if title_elem:
                            title = title_elem.get_text().strip()
                            break

                    # ابحث عن عناصر بكلاسات تحتوي على title
                    if not title:
                        title_elem = parent.find(attrs={'class': lambda x: x and 'title' in ' '.join(x).lower()})
                        if title_elem:
                            title = title_elem.get_text().strip()

            # تنظيف العنوان
            if title:
                title = title.replace('\n', ' ').replace('\t', ' ')
                title = ' '.join(title.split())  # إزالة المسافات الزائدة

            # إذا لم نجد عنوان مناسب، تخطي
            if not title or len(title) < 3:
                continue

            # استخراج رابط الصورة
            img_url = ""
            if img:
                img_url = img.get('src') or img.get('data-src') or img.get('data-lazy-src') or img.get('data-original')
                if img_url:
                    img_url = urljoin(url, img_url)

            # إذا لم نجد صورة ولكن الرابط يحتوي على كلمات مفتاحية، استخدم صورة افتراضية
            if not img_url and has_movie_keywords:
                img_url = "https://via.placeholder.com/300x400?text=Movie"

            # تحديد نوع المحتوى (فيلم أم مسلسل)
            content_type = "movie"  # افتراضي
            title_lower = title.lower()
            href_lower = href.lower()

            # كلمات مفتاحية للمسلسلات
            series_keywords = [
                'series', 'season', 'episode', 'مسلسل', 'موسم', 'حلقة', 'مسلسلات',
                'الموسم', 'الحلقة', 'مدبلج', 'مترجم'
            ]

            if any(keyword in title_lower or keyword in href_lower for keyword in series_keywords):
                content_type = "series"

            # التأكد من أن الرابط يؤدي إلى صفحة محتوى
            content_keywords = ['watch.php', 'movie', 'film', 'series', 'فيلم', 'مسلسل', 'مشاهدة']
            if (any(keyword in href.lower() for keyword in content_keywords) or
                img or has_movie_keywords):
                content_items.append({
                    "title": title,
                    "imageUrl": img_url,
                    "link": href,
                    "type": content_type
                })

        except Exception as e:
            continue

    # إزالة المكررات بناءً على الرابط
    seen_links = set()
    unique_items = []
    for item in content_items:
        if item["link"] not in seen_links:
            seen_links.add(item["link"])
            unique_items.append(item)

    return unique_items

def scrape_worker(session_id, start_url, max_depth, delay, link_filters):
    """دالة الزحف التي تعمل في خيط منفصل"""
    try:
        add_log(session_id, f"🚀 بدء الزحف من: {start_url}")
        add_log(session_id, f"📊 العمق الأقصى: {max_depth}, التأخير: {delay} ثانية")
        add_log(session_id, f"🔍 الفلاتر: {link_filters}")

        visited = set()
        movies_info = []
        series_info = []
        queue = deque([(start_url, 0)])
        domain = urlparse(start_url).netloc

        add_log(session_id, f"🌐 النطاق المستهدف: {domain}")
        update_progress(session_id, 5, "running")

        total_processed = 0
        while queue:
            url, depth = queue.popleft()

            if url in visited or depth > max_depth:
                continue

            visited.add(url)
            total_processed += 1

            add_log(session_id, f"📄 معالجة الصفحة {total_processed}: {url[:50]}...")
            update_progress(session_id, min(10 + (total_processed * 2), 90))

            try:
                add_log(session_id, f"⬇️ تحميل الصفحة...")
                r = requests.get(url, timeout=15)

                if "text/html" not in r.headers.get("Content-Type", ""):
                    add_log(session_id, f"⚠️ تخطي - ليس HTML")
                    continue

                add_log(session_id, f"✅ تم التحميل ({len(r.text)} حرف)")
                soup = BeautifulSoup(r.text, "html.parser")

                # استخراج معلومات المحتوى
                add_log(session_id, f"🔍 استخراج معلومات المحتوى...")

                # إحصائيات تشخيصية
                all_links = soup.find_all("a", href=True)
                links_with_images = [link for link in all_links if link.find('img')]
                add_log(session_id, f"📊 إجمالي الروابط: {len(all_links)}, روابط بصور: {len(links_with_images)}")

                content_items = extract_content_info(soup, url)
                add_log(session_id, f"📋 تم استخراج {len(content_items)} عنصر من الصفحة")

                movies_found = 0
                series_found = 0
                links_added = 0

                # معالجة العناصر المستخرجة
                for item in content_items:
                    # فلترة على أساس domain أولاً
                    if domain not in urlparse(item["link"]).netloc:
                        continue

                    # فحص الفلاتر (إذا كانت موجودة)
                    if link_filters:
                        filter_match = False
                        for f in link_filters:
                            if (f in item["link"].lower() or
                                f in item["title"].lower() or
                                f in item["imageUrl"].lower()):
                                filter_match = True
                                break
                        if not filter_match:
                            continue
                        if item["type"] == "series":
                            series_info.append({
                                "series_name": item["title"],
                                "series_img": item["imageUrl"],
                                "series_href": item["link"]
                            })
                            series_found += 1
                        else:
                            movies_info.append({
                                "movies_name": item["title"],
                                "movies_img": item["imageUrl"],
                                "movies_href": item["link"]
                            })
                            movies_found += 1

                        # إضافة للطابور للزحف العميق
                        if item["link"] not in visited and depth < max_depth:
                            queue.append((item["link"], depth + 1))
                            links_added += 1

                # البحث عن روابط إضافية للصفحات التالية
                pagination_links = soup.find_all("a", href=True)
                for a in pagination_links:
                    href = urljoin(url, a["href"])
                    if domain in urlparse(href).netloc and href not in visited and depth < max_depth:
                        link_text = a.get_text().strip().lower()
                        href_lower = href.lower()

                        # إضافة روابط الصفحات والفئات
                        pagination_keywords = [
                            'next', 'page', 'more', 'التالي', 'المزيد', 'صفحة',
                            'category', 'فئة', 'قسم', 'أفلام', 'مسلسلات'
                        ]

                        # أو إذا كان الرابط يحتوي على معاملات صفحات
                        has_pagination = any(param in href_lower for param in ['page=', '&p=', 'offset='])
                        has_category = 'category.php' in href_lower or 'cat=' in href_lower
                        has_keywords = any(word in link_text for word in pagination_keywords)

                        if has_pagination or has_category or has_keywords:
                            queue.append((href, depth + 1))
                            links_added += 1

                add_log(session_id, f"🎬 وجد {movies_found} فيلم جديد")
                add_log(session_id, f"📺 وجد {series_found} مسلسل جديد")
                add_log(session_id, f"➕ أضيف {links_added} رابط للطابور")

                # تحديث النتائج في الجلسة
                results_data = {
                    "movies_info": movies_info,
                    "series_info": series_info
                }

                with session_lock:
                    if session_id in scraping_sessions:
                        scraping_sessions[session_id]["movies"] = results_data

                # حفظ دوري كل 50 عنصر
                total_items = len(movies_info) + len(series_info)
                if total_items > 0 and total_items % 50 == 0:
                    try:
                        temp_filename = f"content_temp_{session_id[:8]}.json"
                        with open(temp_filename, 'w', encoding='utf-8') as f:
                            json.dump(results_data, f, ensure_ascii=False, indent=2)
                        add_log(session_id, f"💾 حفظ مؤقت: {len(movies_info)} فيلم، {len(series_info)} مسلسل")
                    except Exception as e:
                        add_log(session_id, f"⚠️ خطأ في الحفظ المؤقت: {str(e)}", "error")

            except Exception as e:
                add_log(session_id, f"⚠️ خطأ في تحميل {url[:30]}...: {str(e)}", "error")
                continue

            add_log(session_id, f"⏳ انتظار {delay} ثانية...")
            time.sleep(delay)

        # انتهاء العملية
        total_movies = len(movies_info)
        total_series = len(series_info)
        add_log(session_id, f"🎉 انتهت العملية! وجد {total_movies} فيلم و {total_series} مسلسل")
        update_progress(session_id, 100, "completed")

        # إعداد البيانات النهائية
        final_results = {
            "movies_info": movies_info,
            "series_info": series_info
        }

        # حفظ النتائج في ملف
        try:
            filename = f"content_{session_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(final_results, f, ensure_ascii=False, indent=2)
            add_log(session_id, f"💾 تم حفظ النتائج في ملف: {filename}")
        except Exception as e:
            add_log(session_id, f"⚠️ خطأ في حفظ الملف: {str(e)}", "error")

        with session_lock:
            if session_id in scraping_sessions:
                scraping_sessions[session_id]["movies"] = final_results

    except Exception as e:
        add_log(session_id, f"❌ خطأ عام: {str(e)}", "error")
        update_progress(session_id, 0, "error")

# صفحة HTML لو فتحت السيرفر مباشرة
@app.route("/")
def home():
    # خدمة ملف index.html مباشرة
    with open('index.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route("/api/scrape", methods=["POST"])
def api_scrape():
    """بدء عملية زحف جديدة"""
    cfg = request.json
    start_url = cfg.get("start_url")
    max_depth = int(cfg.get("max_depth", 2))
    delay = float(cfg.get("delay", 1))

    # معالجة link_filter
    link_filter_raw = cfg.get("link_filter", "")
    if isinstance(link_filter_raw, list):
        link_filters = [f.lower().strip() for f in link_filter_raw if f.strip()]
    else:
        link_filters = [f.lower().strip() for f in str(link_filter_raw).split(',') if f.strip()]

    if not start_url:
        return jsonify({"error": "❌ start_url مطلوب"}), 400

    # إنشاء جلسة جديدة
    session_id = str(uuid.uuid4())

    with session_lock:
        scraping_sessions[session_id] = {
            "status": "starting",
            "progress": 0,
            "logs": [],
            "movies": [],
            "thread": None
        }

    # بدء الزحف في خيط منفصل
    thread = threading.Thread(
        target=scrape_worker,
        args=(session_id, start_url, max_depth, delay, link_filters)
    )
    thread.daemon = True
    thread.start()

    with session_lock:
        scraping_sessions[session_id]["thread"] = thread

    return jsonify({
        "session_id": session_id,
        "message": "تم بدء عملية الزحف",
        "status": "started"
    })

@app.route("/api/status/<session_id>", methods=["GET"])
def get_status(session_id):
    """الحصول على حالة عملية الزحف"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        movies_data = session["movies"]

        # حساب عدد العناصر
        movies_count = 0
        series_count = 0
        if isinstance(movies_data, dict):
            movies_count = len(movies_data.get("movies_info", []))
            series_count = len(movies_data.get("series_info", []))

        return jsonify({
            "status": session["status"],
            "progress": session["progress"],
            "logs": session["logs"][-10:],  # آخر 10 رسائل
            "movies_count": movies_count,
            "series_count": series_count,
            "total_count": movies_count + series_count
        })

@app.route("/api/results/<session_id>", methods=["GET"])
def get_results(session_id):
    """الحصول على نتائج الزحف"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        return jsonify({
            "status": session["status"],
            "progress": session["progress"],
            "movies": session["movies"]
        })

@app.route("/api/logs/<session_id>", methods=["GET"])
def get_logs(session_id):
    """الحصول على جميع السجلات"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        return jsonify({
            "logs": session["logs"],
            "status": session["status"],
            "progress": session["progress"]
        })

@app.route("/api/current-results/<session_id>", methods=["GET"])
def get_current_results(session_id):
    """الحصول على النتائج الحالية حتى لو لم تكتمل العملية"""
    with session_lock:
        if session_id not in scraping_sessions:
            return jsonify({"error": "جلسة غير موجودة"}), 404

        session = scraping_sessions[session_id]
        movies_data = session["movies"]

        # حساب العدد الإجمالي
        total_count = 0
        if isinstance(movies_data, dict):
            movies_count = len(movies_data.get("movies_info", []))
            series_count = len(movies_data.get("series_info", []))
            total_count = movies_count + series_count

        return jsonify({
            "data": movies_data,
            "count": total_count,
            "status": session["status"],
            "progress": session["progress"]
        })

if __name__ == "__main__":
    app.run(debug=True)
