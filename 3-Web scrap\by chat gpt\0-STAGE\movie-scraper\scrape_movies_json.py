import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from collections import deque
import json
import time
import urllib.robotparser
import sys

# --------- إعدادات ---------
# !!! هام: قم بتغيير هذا الرابط إلى الموقع الذي تريد استهدافه
start_url = "https://example.com"
max_depth = 2   # العمق الأقصى للبحث
use_selenium = True  # غيّرها إلى True لو الموقع بيعتمد على جافاسكربت
delay = 1  # ثواني بين الطلبات
output_file = "movies.json"

def main(start_url):
    """الدالة الرئيسية لتشغيل عملية البحث."""
    # --------- تهيئة ---------
    visited = set()
    queue = deque([(start_url, 0)])
    movies = []
    USER_AGENT = "MovieScraperBot/1.0"

    # --------- التحقق من robots.txt ---------
    domain = f"{urlparse(start_url).scheme}://{urlparse(start_url).netloc}"
    robots_url = urljoin(domain, "/robots.txt")
    rp = urllib.robotparser.RobotFileParser()
    rp.set_url(robots_url)
    try:
        rp.read()
        print(f"ℹ️ تم قراءة {robots_url} بنجاح.")
    except Exception as e:
        print(f"⚠️ تعذّر قراءة robots.txt: {e}. سيتم المتابعة بدون التحقق.")

    # --------- Selenium (لو مطلوب) ---------
    driver = None
    if use_selenium:
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            options = Options()
            options.add_argument("--headless")
            options.add_argument(f"user-agent={USER_AGENT}")
            driver = webdriver.Chrome(options=options)
            print("ℹ️ تم تهيئة Selenium.")
        except ImportError:
            print("⚠️ خطأ: مكتبة Selenium غير مثبتة. يرجى تثبيتها بـ 'pip install selenium'.")
            use_selenium = False # تعطيل Selenium إذا لم تكن المكتبة موجودة

    # --------- BFS (البحث بالعرض أولاً) مع تتبع العمق ---------
    print("\n🚀 بدء عملية الزحف...")
    while queue:
        current_url, depth = queue.popleft()

        if current_url in visited:
            continue

        if depth > max_depth:
            print(f"➡️ تخطي: {current_url} (تجاوز العمق الأقصى)")
            continue

        html = fetch_page(current_url, rp, USER_AGENT, driver)
        if not html:
            continue

        visited.add(current_url)
        soup = BeautifulSoup(html, "html.parser")

        # محاولة استخراج بيانات الفيلم من الصفحة الحالية
        movie_data = extract_movie_data(soup, current_url)
        if movie_data:
            # التأكد من عدم إضافة نفس الفيلم مرتين
            if movie_data not in movies:
                print(f"✅ تم العثور على فيلم: {movie_data['name']}")
                movies.append(movie_data)

        # البحث عن روابط جديدة لإضافتها إلى قائمة الانتظار
        if depth < max_depth:
            for a in soup.find_all("a", href=True):
                link = urljoin(current_url, a["href"])
                # التحقق من أن الرابط يتبع نفس النطاق وليس في الروابط التي تمت زيارتها بالفعل
                if urlparse(link).netloc == urlparse(start_url).netloc and link not in visited:
                    queue.append((link, depth + 1))

        # تأخير لتجنب الضغط على السيرفر
        time.sleep(delay)

    # --------- حفظ النتائج في JSON ---------
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(movies, f, ensure_ascii=False, indent=4)
        print(f"\n🎉 اكتملت العملية. تم العثور على {len(movies)} فيلم/أفلام.")
        print(f"📂 تم حفظ النتائج في {output_file}")
    except IOError as e:
        print(f"⚠️ خطأ في حفظ الملف: {e}")


    if use_selenium and driver:
        driver.quit()
        print("ℹ️ تم إغلاق Selenium.")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        main(sys.argv[1])
    else:
        print("الرجاء توفير رابط البداية كوسيط (argument).")
        print("مثال: python scrape_movies_json.py https://example.com")
        sys.exit(1)
