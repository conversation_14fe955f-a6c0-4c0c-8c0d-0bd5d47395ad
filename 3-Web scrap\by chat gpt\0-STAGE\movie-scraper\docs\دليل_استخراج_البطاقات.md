# دليل استخراج بطاقات الأفلام والمسلسلات

## 🎯 نظرة عامة

تم تطوير النظام ليركز على استخراج **بطاقات الأفلام والمسلسلات فقط** من صفحات التصنيفات، مع تجنب الدخول في صفحات العرض أو الفيديو. النظام يدعم التصفية حسب نوع المحتوى ويلتزم بالبقاء في نفس التصنيف المدخل.

## ✨ الميزات الجديدة

### 1. استخراج البطاقات فقط
- **تركيز على البطاقات**: استخراج روابط البطاقات وليس صفحات العرض
- **تجنب صفحات الفيديو**: تجاهل روابط `watch.php`, `play.php`, `view.php`
- **البقاء في التصنيف**: عدم الخروج من التصنيف المحدد

### 2. فلترة نوع المحتوى
- **أفلام فقط**: `content_filter: "movies"`
- **مسلسلات فقط**: `content_filter: "series"`
- **الكل**: `content_filter: "all"` (افتراضي)

### 3. التصفح الذكي
- **البقاء في نفس التصنيف**: التنقل فقط في صفحات نفس القسم
- **تعرف على أزرار التصفح**: "التالي"، أرقام الصفحات، إلخ
- **تجنب الأقسام الأخرى**: عدم الانتقال لتصنيفات مختلفة

## 🎬 البيانات المستخرجة

لكل بطاقة فيلم/مسلسل:

```json
{
  "title": "اسم الفيلم/المسلسل",
  "imageUrl": "رابط صورة البوستر", 
  "link": "رابط البطاقة (وليس صفحة العرض)",
  "type": "movie" // أو "series"
}
```

## 🚀 كيفية الاستخدام

### 1. عبر الواجهة الويب

1. افتح المتصفح على `http://localhost:5000`
2. أدخل رابط صفحة التصنيف (مثل: `https://example.com/category/movies`)
3. اختر نوع المحتوى:
   - **الكل**: أفلام ومسلسلات
   - **أفلام فقط**: الأفلام فقط
   - **مسلسلات فقط**: المسلسلات فقط
4. اضبط العمق والتأخير
5. ابدأ الاستخراج

### 2. عبر API

```bash
POST /api/scrape
Content-Type: application/json

{
  "start_url": "https://example.com/category/foreign-movies",
  "max_depth": 3,
  "delay": 2,
  "content_filter": "movies",
  "link_filter": ""
}
```

### 3. معاملات API

| المعامل | الوصف | القيم المقبولة | افتراضي |
|---------|--------|----------------|----------|
| `start_url` | رابط صفحة التصنيف | URL صالح | مطلوب |
| `max_depth` | العمق الأقصى | رقم موجب | 2 |
| `delay` | التأخير بالثواني | رقم موجب | 1 |
| `content_filter` | نوع المحتوى | `all`, `movies`, `series` | `all` |
| `link_filter` | فلتر الروابط | نص مفصول بفواصل | فارغ |

## 📊 أمثلة عملية

### مثال 1: استخراج الأفلام الأجنبية فقط
```json
{
  "start_url": "https://cimanow.cc/category/افلام-اجنبية/",
  "max_depth": 3,
  "delay": 2,
  "content_filter": "movies"
}
```

### مثال 2: استخراج المسلسلات العربية فقط
```json
{
  "start_url": "https://example.com/category/arabic-series/",
  "max_depth": 2,
  "delay": 1,
  "content_filter": "series"
}
```

### مثال 3: استخراج كل المحتوى
```json
{
  "start_url": "https://a.asd.homes/category/foreign-movies-6/",
  "max_depth": 4,
  "delay": 1.5,
  "content_filter": "all"
}
```

## 🔍 كيف يعمل النظام

### 1. تحليل الصفحة
```
صفحة التصنيف → تحليل HTML → استخراج البطاقات → فلترة النوع
```

### 2. التحقق من البطاقات
- ✅ `/movie/avengers-2019` - بطاقة فيلم
- ✅ `/series/breaking-bad` - بطاقة مسلسل  
- ✅ `/film.php?id=123` - بطاقة بمعامل
- ❌ `/watch.php?id=123` - صفحة مشاهدة
- ❌ `/play/movie-stream` - صفحة تشغيل

### 3. البحث عن الصفحات التالية
- البحث عن أزرار "التالي" و "المزيد"
- البحث عن أرقام الصفحات (1, 2, 3...)
- التأكد من البقاء في نفس التصنيف

## 📁 تنظيم الملفات

```
movie-scraper/
├── results/           # ملفات النتائج JSON
├── docs/             # ملفات التوثيق
├── app.py            # الملف الرئيسي
├── extraction_config.py  # إعدادات الاستخراج
└── test_card_extraction.py  # اختبار النظام
```

## 🧪 اختبار النظام

### اختبار سريع
```bash
python test_card_extraction.py
```

### اختبار مع موقع حقيقي
```bash
python test_card_extraction.py
# اختر 'y' عند السؤال عن المواقع الحقيقية
```

## 📊 فهم النتائج

### ملف النتائج
```json
{
  "session_info": {
    "session_id": "abc123...",
    "start_url": "https://example.com/category/movies",
    "content_filter": "movies",
    "total_cards": 45,
    "extraction_quality": 0.92
  },
  "data": {
    "movies_info": [
      {
        "movies_name": "Avengers: Endgame",
        "movies_img": "https://example.com/poster.jpg",
        "movies_href": "https://example.com/movie/avengers"
      }
    ],
    "series_info": []
  }
}
```

### رسائل النظام
```
🎯 أفضل قاعدة: rule_generic_movie_cards
📊 جودة الاستخراج: 0.92
🔗 وجد 3 رابط للصفحات التالية في نفس التصنيف
📋 تم استخراج 25 بطاقة من الصفحة
```

## ⚙️ الإعدادات المتقدمة

### تخصيص موقع معين
في `extraction_config.py`:

```python
SITE_SPECIFIC_RULES = {
    'yoursite.com': {
        'selectors': ['.movie-card', '.film-item'],
        'title_selectors': ['.movie-title', 'h2'],
        'image_selectors': ['.poster img'],
        'link_selectors': ['.movie-link a']
    }
}
```

### ضبط فلترة البطاقات
```python
# أنماط صفحات العرض (يجب تجنبها)
VIDEO_PAGE_PATTERNS = [
    'watch.php', 'play.php', 'view.php', 'stream.php'
]

# أنماط بطاقات الأفلام (مقبولة)
CARD_PATTERNS = [
    '/movie/', '/film/', '/series/', '/show/'
]
```

## 🔧 استكشاف الأخطاء

### مشكلة: لا يستخرج بطاقات
**الحلول:**
1. تحقق من أن الرابط يؤدي لصفحة تصنيف
2. تحقق من بنية HTML للموقع
3. أضف قاعدة مخصصة للموقع

### مشكلة: يستخرج صفحات عرض
**الحلول:**
1. تحقق من دالة `_is_card_link()`
2. أضف أنماط جديدة لصفحات العرض
3. اضبط فلترة الروابط

### مشكلة: لا يجد الصفحات التالية
**الحلول:**
1. تحقق من بنية التصفح في الموقع
2. أضف كلمات مفتاحية للتصفح
3. تحقق من معاملات URL

## 📈 نصائح للحصول على أفضل النتائج

### 1. اختيار الرابط المناسب
- ✅ `https://site.com/category/movies`
- ✅ `https://site.com/genre/action`
- ❌ `https://site.com/watch/movie-123`
- ❌ `https://site.com/player.php`

### 2. ضبط العمق
- **العمق 1-2**: للتصنيفات الصغيرة
- **العمق 3-4**: للتصنيفات المتوسطة
- **العمق 5+**: للتصنيفات الكبيرة

### 3. استخدام الفلاتر
- استخدم `content_filter` لتحديد نوع المحتوى
- استخدم `link_filter` للبحث عن كلمات معينة
- اضبط `delay` حسب سرعة الموقع

## 🎉 الخلاصة

النظام الآن مُحسَّن لـ:
- **استخراج البطاقات فقط** وليس صفحات العرض
- **البقاء في نفس التصنيف** المحدد
- **فلترة نوع المحتوى** (أفلام/مسلسلات/الكل)
- **تنظيم أفضل للملفات** والنتائج
- **دقة أعلى** في الاستخراج

**النظام جاهز لاستخراج بطاقات الأفلام والمسلسلات بكفاءة عالية! 🎬**
