1) تحضير البيئة الأساسية

نزّل وَثبّت بايثون

اذهب إلى https://www.python.org/
 ونزّل أحدث إصدار 3.x.

أثناء التثبيت فعل خيار Add Python to PATH (مهم).

افتح الطرفية (Terminal)

ويندوز: افتح PowerShell أو Command Prompt.

انشئ مجلد المشروع

mkdir movie-scraper
ثم بعده 
cd movie-scraper


أنصح بإنشاء virtual environment (اختياري لكن موصى به)

python -m venv venv
# تفعيل الـ venv:
# ويندوز (PowerShell):
.\venv\Scripts\Activate.ps1
# أو ويندوز (cmd):
.\venv\Scripts\activate


نصب المكتبات المطلوبة

pip install requests beautifulsoup4 lxml


لو هتتعامل مع مواقع تعتمد على جافاسكربت استخدم Selenium أو Playwright. مثال سريع:

pip install selenium
# أو
pip install playwright
python -m playwright install