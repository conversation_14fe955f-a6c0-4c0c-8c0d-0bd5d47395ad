@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    نظام الاستخراج المتقدم للأفلام
echo ========================================
echo.

:menu
echo اختر العملية المطلوبة:
echo.
echo 1. اختبار سريع للنظام
echo 2. تشغيل الخادم
echo 3. اختبار شامل
echo 4. عرض الدليل
echo 5. خروج
echo.
set /p choice="أدخل رقم الخيار (1-5): "

if "%choice%"=="1" goto quick_test
if "%choice%"=="2" goto run_server
if "%choice%"=="3" goto full_test
if "%choice%"=="4" goto show_guide
if "%choice%"=="5" goto exit
goto invalid

:quick_test
echo.
echo 🚀 تشغيل الاختبار السريع...
echo ================================
python quick_test.py
echo.
echo ✅ انتهى الاختبار السريع!
echo تحقق من ملف: quick_test_results.json
echo.
pause
goto menu

:run_server
echo.
echo 🌐 تشغيل الخادم...
echo ==================
echo الخادم سيعمل على: http://localhost:5000
echo اضغط Ctrl+C لإيقاف الخادم
echo.
python app.py
pause
goto menu

:full_test
echo.
echo 🧪 تشغيل الاختبار الشامل...
echo =============================
python test_extraction.py
echo.
echo ✅ انتهى الاختبار الشامل!
echo.
pause
goto menu

:show_guide
echo.
echo 📖 عرض الدليل...
echo ================
if exist "دليل_النظام_المتقدم.md" (
    start "" "دليل_النظام_المتقدم.md"
) else (
    echo ❌ ملف الدليل غير موجود!
)
echo.
pause
goto menu

:invalid
echo.
echo ❌ خيار غير صحيح! يرجى اختيار رقم من 1 إلى 5
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام النظام المتقدم!
echo.
pause
exit
