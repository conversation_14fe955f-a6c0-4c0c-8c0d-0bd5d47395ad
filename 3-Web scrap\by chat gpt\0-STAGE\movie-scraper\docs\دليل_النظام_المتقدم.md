# دليل النظام المتقدم لاستخراج الأفلام والمسلسلات

## 🎯 نظرة عامة

تم تطوير نظام متقدم لاستخراج أسماء الأفلام وروابطها وصور الأفلام من المواقع المختلفة مع التعرف التلقائي على أفضل قواعد الاستخراج لكل موقع.

## ✨ الميزات الجديدة

### 1. نظام قواعد الاستخراج المتعدد
- **8 قواعد استخراج مختلفة** تعمل تلقائياً
- **تقييم جودة ذكي** لاختيار أفضل قاعدة
- **دعم شامل** للمواقع العربية والأجنبية
- **تعرف تلقائي** على بطاقات الأفلام والمسلسلات

### 2. البحث الذكي عن الصفحات التالية
- **5 استراتيجيات مختلفة** للعثور على روابط الصفحات التالية
- **تعرف تلقائي** على أزرار "التالي" و "المزيد"
- **دعم الترقيم** وأرقام الصفحات
- **تعرف على الرموز** والأسهم (»، >، →)

### 3. نظام التقييم والجودة
- **تقييم شامل** لجودة الاستخراج
- **نقاط جودة** للعناوين والصور والروابط
- **اختيار تلقائي** لأفضل النتائج
- **تقارير مفصلة** عن أداء كل قاعدة

## 🚀 كيفية الاستخدام

### 1. التشغيل العادي
```bash
# تشغيل الخادم
python app.py
```

ثم افتح المتصفح على: `http://localhost:5000`

### 2. الاختبار السريع
```bash
# اختبار النظام
python quick_test.py
```

### 3. الاختبار المتقدم
```bash
# اختبار شامل مع مواقع حقيقية
python test_extraction.py
```

## 📋 قواعد الاستخراج المتاحة

| القاعدة | الوصف | الاستخدام |
|---------|--------|----------|
| `rule_generic_movie_cards` | بطاقات الأفلام العامة | `.movie-card`, `.film-card` |
| `rule_arabic_movie_sites` | المواقع العربية | `.فيلم`, `.مسلسل` |
| `rule_wordpress_themes` | مواقع WordPress | `.post`, `.entry` |
| `rule_bootstrap_cards` | بطاقات Bootstrap | `.card`, `.media` |
| `rule_flex_grid_layouts` | التخطيطات المرنة | `.grid-item`, `.flex-item` |
| `rule_list_based_layouts` | قوائم HTML | عناصر `<li>` |
| `rule_table_based_layouts` | جداول HTML | عناصر `<tr>` |
| `rule_custom_movie_sites` | مواقع مخصصة | cimanow.cc، asd.homes |

## 🎬 مثال على الاستخدام

### استخراج من موقع عربي
```json
{
  "start_url": "https://cimanow.cc/category/افلام-اجنبية/",
  "max_depth": 3,
  "delay": 2,
  "link_filter": ""
}
```

### استخراج من موقع أجنبي
```json
{
  "start_url": "https://a.asd.homes/category/foreign-movies-6/",
  "max_depth": 2,
  "delay": 1,
  "link_filter": "movie,film"
}
```

## 📊 فهم النتائج

### تقرير الجودة
```
🎯 أفضل قاعدة: rule_generic_movie_cards
📊 جودة الاستخراج: 0.85
🔧 قواعد مستخدمة: rule_generic_movie_cards, rule_wordpress_themes
```

### النتائج المستخرجة
```json
{
  "movies_info": [
    {
      "movies_name": "Avengers: Endgame",
      "movies_img": "https://example.com/poster.jpg",
      "movies_href": "https://example.com/watch.php?id=123"
    }
  ],
  "series_info": [
    {
      "series_name": "Breaking Bad",
      "series_img": "https://example.com/series.jpg",
      "series_href": "https://example.com/series/bb"
    }
  ]
}
```

## ⚙️ الإعدادات المتقدمة

### تخصيص موقع معين
في ملف `extraction_config.py`:

```python
SITE_SPECIFIC_RULES = {
    'yoursite.com': {
        'selectors': ['.custom-movie-card'],
        'title_selectors': ['.custom-title'],
        'image_selectors': ['.custom-image img'],
        'link_selectors': ['.custom-link a']
    }
}
```

### ضبط معايير الجودة
```python
EXTRACTION_SETTINGS = {
    'min_quality_threshold': 0.3,  # الحد الأدنى للجودة
    'max_items_per_page': 100,     # الحد الأقصى للعناصر
    'advanced_title_cleaning': True # تنظيف متقدم للعناوين
}
```

## 🔍 استكشاف الأخطاء

### مشكلة: لا يتم استخراج محتوى
**الأسباب المحتملة:**
- جودة الاستخراج منخفضة
- بنية HTML غير مدعومة
- حماية من الموقع

**الحلول:**
1. خفض `min_quality_threshold` إلى 0.1
2. إضافة قاعدة مخصصة للموقع
3. زيادة `delay` بين الطلبات

### مشكلة: عناوين غير صحيحة
**الحلول:**
1. تفعيل `advanced_title_cleaning`
2. إضافة كلمات للإزالة في `remove_from_titles`
3. ضبط `title_cleaning_patterns`

### مشكلة: لا يجد الصفحات التالية
**الحلول:**
1. فحص بنية HTML للموقع
2. إضافة كلمات مفتاحية جديدة
3. تخصيص `pagination_selectors`

## 📈 نصائح للحصول على أفضل النتائج

### 1. اختيار العمق المناسب
- **العمق 1-2**: للمواقع الصغيرة (أقل من 100 صفحة)
- **العمق 3-4**: للمواقع المتوسطة (100-1000 صفحة)
- **العمق 5+**: للمواقع الكبيرة (احذر من الحمل الزائد)

### 2. ضبط التأخير
- **1 ثانية**: للمواقع السريعة
- **2-3 ثواني**: للمواقع العادية
- **5+ ثواني**: للمواقع المحمية

### 3. استخدام الفلاتر
```json
{
  "link_filter": "movie,film,فيلم,مسلسل"
}
```

## 🛠️ الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `app.py` | الملف الرئيسي للخادم |
| `extraction_config.py` | إعدادات الاستخراج |
| `quick_test.py` | اختبار سريع |
| `test_extraction.py` | اختبار شامل |
| `README_ADVANCED.md` | دليل تقني مفصل |

## 🎉 مثال كامل

```bash
# 1. تشغيل الاختبار السريع
python quick_test.py

# 2. تشغيل الخادم
python app.py

# 3. إرسال طلب عبر المتصفح أو API
POST /api/scrape
{
  "start_url": "https://example.com/movies",
  "max_depth": 3,
  "delay": 2
}

# 4. متابعة التقدم
GET /api/status/{session_id}

# 5. الحصول على النتائج
GET /api/results/{session_id}
```

## 📞 الدعم والمساعدة

1. **راجع السجلات**: تحقق من رسائل النظام
2. **استخدم الاختبار**: جرب `quick_test.py`
3. **اضبط الإعدادات**: عدّل `extraction_config.py`
4. **تحقق من الجودة**: راقب نقاط الجودة

---

**النظام الآن جاهز للاستخدام مع دعم شامل لجميع أنواع مواقع الأفلام والمسلسلات! 🎬**
