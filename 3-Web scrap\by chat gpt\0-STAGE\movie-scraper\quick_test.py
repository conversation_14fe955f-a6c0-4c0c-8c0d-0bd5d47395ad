# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام الجديد
"""

from app import ExtractionRules, extract_content_info_with_details, find_next_page_links
from bs4 import BeautifulSoup
import json

def quick_test():
    """اختبار سريع للنظام"""
    print("🚀 اختبار سريع للنظام الجديد")
    print("=" * 40)
    
    # HTML تجريبي
    test_html = """
    <html>
    <head><title>موقع الأفلام</title></head>
    <body>
        <div class="movie-card">
            <a href="/movie/avengers-endgame">
                <img src="/posters/avengers.jpg" alt="Avengers: Endgame">
                <h3>Avengers: Endgame</h3>
            </a>
        </div>
        
        <div class="movie-card">
            <a href="/movie/joker-2019">
                <img src="/posters/joker.jpg" alt="Joker (2019)">
                <h3>Joker</h3>
            </a>
        </div>
        
        <article class="post">
            <h2><a href="/watch.php?id=123">فيلم الأكشن الجديد</a></h2>
            <img src="/images/action-movie.jpg" alt="فيلم الأكشن">
            <p>مشاهدة فيلم الأكشن الجديد اونلاين</p>
        </article>
        
        <div class="series-item">
            <a href="/series/breaking-bad">
                <img data-src="/series/bb.jpg" alt="Breaking Bad">
                <div class="title">Breaking Bad - مسلسل</div>
            </a>
        </div>
        
        <div class="pagination">
            <a href="/page/2" class="next">التالي</a>
            <a href="/page/2">2</a>
            <a href="/page/3">3</a>
            <a href="/more-movies">المزيد من الأفلام</a>
        </div>
    </body>
    </html>
    """
    
    soup = BeautifulSoup(test_html, 'html.parser')
    base_url = "https://example-movies.com"
    
    print("1️⃣ اختبار قواعد الاستخراج:")
    print("-" * 30)
    
    # اختبار الاستخراج المتقدم
    content_items, extraction_info = extract_content_info_with_details(soup, base_url)
    
    print(f"✅ تم استخراج {len(content_items)} عنصر")
    
    if extraction_info:
        print(f"🎯 أفضل قاعدة: {extraction_info['best_rule']}")
        print(f"📊 جودة الاستخراج: {extraction_info['best_score']:.2f}")
        print(f"🔧 عدد القواعد المختبرة: {extraction_info['total_rules_tested']}")
        
        print(f"\n📋 تفاصيل جميع القواعد:")
        for result in extraction_info['rule_results']:
            print(f"  - {result['rule']}: {result['count']} عنصر (جودة: {result['quality_score']:.2f})")
    
    print(f"\n🎬 النتائج المستخرجة:")
    for i, item in enumerate(content_items, 1):
        print(f"  {i}. العنوان: {item['title']}")
        print(f"     النوع: {item['type']}")
        print(f"     الرابط: {item['link']}")
        print(f"     الصورة: {'✅ موجودة' if 'placeholder' not in item['imageUrl'] else '❌ افتراضية'}")
        print()
    
    print("2️⃣ اختبار البحث عن الصفحات التالية:")
    print("-" * 30)
    
    next_links = find_next_page_links(soup, base_url)
    print(f"✅ وجد {len(next_links)} رابط للصفحات التالية:")
    
    for i, link in enumerate(next_links, 1):
        print(f"  {i}. {link}")
    
    print(f"\n3️⃣ حفظ النتائج:")
    print("-" * 30)
    
    # حفظ النتائج
    results = {
        'test_info': {
            'total_items': len(content_items),
            'extraction_quality': extraction_info['best_score'] if extraction_info else 0,
            'best_rule': extraction_info['best_rule'] if extraction_info else 'unknown',
            'next_pages_found': len(next_links)
        },
        'movies': [item for item in content_items if item['type'] == 'movie'],
        'series': [item for item in content_items if item['type'] == 'series'],
        'next_page_links': next_links
    }
    
    try:
        with open('quick_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print("✅ تم حفظ النتائج في: quick_test_results.json")
    except Exception as e:
        print(f"❌ خطأ في الحفظ: {str(e)}")
    
    print(f"\n🎉 انتهى الاختبار السريع!")
    print(f"📊 الملخص:")
    print(f"  - العناصر المستخرجة: {len(content_items)}")
    print(f"  - الأفلام: {len([item for item in content_items if item['type'] == 'movie'])}")
    print(f"  - المسلسلات: {len([item for item in content_items if item['type'] == 'series'])}")
    print(f"  - الصفحات التالية: {len(next_links)}")
    
    return results

if __name__ == "__main__":
    quick_test()
