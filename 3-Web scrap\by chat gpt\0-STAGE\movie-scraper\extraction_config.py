# -*- coding: utf-8 -*-
"""
ملف إعدادات الاستخراج المتقدم
يحتوي على قواعد وإعدادات مخصصة للمواقع المختلفة
"""

# إعدادات عامة للاستخراج
EXTRACTION_SETTINGS = {
    # الحد الأدنى لجودة القاعدة
    'min_quality_threshold': 0.3,
    
    # الحد الأقصى لعدد العناصر المستخرجة من صفحة واحدة
    'max_items_per_page': 100,
    
    # الحد الأدنى لطول العنوان
    'min_title_length': 3,
    
    # الحد الأقصى لطول العنوان
    'max_title_length': 200,
    
    # تفعيل التنظيف المتقدم للعناوين
    'advanced_title_cleaning': True,
    
    # تفعيل اكتشاف نوع المحتوى التلقائي
    'auto_content_type_detection': True
}

# قواعد مخصصة للمواقع المعروفة
SITE_SPECIFIC_RULES = {
    'cimanow.cc': {
        'selectors': [
            '.post', '.movie-item', '.film-item', '.content-item',
            '.entry', '.article', '.post-content'
        ],
        'title_selectors': [
            '.post-title', '.entry-title', '.movie-title', '.film-title',
            'h1', 'h2', 'h3', '.title'
        ],
        'image_selectors': [
            '.post-thumbnail img', '.movie-poster img', '.film-poster img',
            '.featured-image img', '.attachment-post-thumbnail'
        ],
        'link_selectors': [
            '.post-title a', '.entry-title a', '.movie-link a',
            '.read-more a', '.permalink'
        ],
        'pagination_selectors': [
            '.pagination a', '.page-numbers a', '.nav-links a',
            '.next-page', '.load-more'
        ]
    },
    
    'asd.homes': {
        'selectors': [
            '.entry', '.post-item', '.content-item', '.movie-card',
            '.film-card', '.post', '.article'
        ],
        'title_selectors': [
            '.entry-title', '.post-title', '.movie-name', '.film-name',
            'h2', 'h3', '.title', '.name'
        ],
        'image_selectors': [
            '.entry-image img', '.post-image img', '.movie-image img',
            '.thumbnail img', '.featured-image img'
        ],
        'link_selectors': [
            '.entry-title a', '.post-title a', '.movie-link a',
            '.entry-link a', '.permalink'
        ],
        'pagination_selectors': [
            '.pagination a', '.page-nav a', '.next-posts a',
            '.load-more', '.show-more'
        ]
    }
}

# كلمات مفتاحية للتعرف على المحتوى
CONTENT_KEYWORDS = {
    'movies': [
        'movie', 'film', 'فيلم', 'cinema', 'سينما', 'watch', 'مشاهدة',
        'download', 'تحميل', 'streaming', 'بث', 'online', 'اونلاين'
    ],
    
    'series': [
        'series', 'season', 'episode', 'مسلسل', 'موسم', 'حلقة',
        'tv show', 'برنامج تلفزيوني', 'مسلسلات', 'الموسم', 'الحلقة'
    ],
    
    'skip_keywords': [
        'login', 'register', 'admin', 'search', 'contact', 'about',
        'privacy', 'terms', 'policy', 'تسجيل', 'دخول', 'بحث',
        'اتصل', 'حول', 'سياسة', 'شروط'
    ]
}

# أنماط URL للتعرف على صفحات المحتوى
URL_PATTERNS = {
    'movie_pages': [
        r'/movie/',
        r'/film/',
        r'/watch\.php',
        r'/view\.php',
        r'/play\.php',
        r'[?&]movie=',
        r'[?&]film=',
        r'/فيلم/',
        r'/مشاهدة/'
    ],
    
    'series_pages': [
        r'/series/',
        r'/season/',
        r'/episode/',
        r'[?&]series=',
        r'[?&]season=',
        r'[?&]episode=',
        r'/مسلسل/',
        r'/موسم/',
        r'/حلقة/'
    ],
    
    'pagination_pages': [
        r'[?&]page=\d+',
        r'[?&]p=\d+',
        r'[?&]offset=\d+',
        r'[?&]start=\d+',
        r'/page/\d+',
        r'/صفحة/\d+'
    ]
}

# إعدادات التنظيف والتصفية
CLEANING_RULES = {
    # نصوص يجب إزالتها من العناوين
    'remove_from_titles': [
        'مشاهدة', 'تحميل', 'watch', 'download', 'online', 'free',
        'hd', 'full', 'movie', 'film', 'اونلاين', 'مجاني', 'كامل'
    ],
    
    # أنماط regex للتنظيف
    'title_cleaning_patterns': [
        r'\s*-\s*مشاهدة.*$',
        r'\s*-\s*تحميل.*$',
        r'\s*-\s*watch.*$',
        r'\s*-\s*download.*$',
        r'\([^)]*\d{4}[^)]*\)',  # إزالة السنوات بين أقواس
        r'\s+',  # دمج المسافات المتعددة
    ],
    
    # حد أدنى وأقصى لطول العنوان بعد التنظيف
    'min_cleaned_title_length': 2,
    'max_cleaned_title_length': 150
}

# إعدادات الصور
IMAGE_SETTINGS = {
    # أنواع الصور المقبولة
    'accepted_extensions': ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    
    # الحد الأدنى لحجم الصورة (بالبكسل)
    'min_image_size': 100,
    
    # صورة افتراضية
    'default_image': "https://via.placeholder.com/300x400?text=Movie",
    
    # مصادر الصور المختلفة للبحث
    'image_sources': [
        'src', 'data-src', 'data-lazy-src', 'data-original',
        'data-srcset', 'data-lazy', 'data-echo'
    ]
}

# إعدادات الروابط
LINK_SETTINGS = {
    # أنماط الروابط المقبولة
    'accepted_link_patterns': [
        r'https?://',
        r'/[^/]+'
    ],
    
    # أنماط الروابط المرفوضة
    'rejected_link_patterns': [
        r'javascript:',
        r'mailto:',
        r'tel:',
        r'#$',
        r'\.pdf$',
        r'\.doc$',
        r'\.zip$'
    ]
}

# إعدادات التقييم
QUALITY_EVALUATION = {
    # أوزان معايير التقييم
    'weights': {
        'title_quality': 0.3,
        'image_quality': 0.2,
        'link_quality': 0.3,
        'uniqueness': 0.2
    },
    
    # نقاط إضافية
    'bonus_points': {
        'has_movie_keywords': 0.1,
        'has_watch_params': 0.1,
        'reasonable_quantity': 0.1,
        'high_uniqueness': 0.05
    },
    
    # حدود الكمية المعقولة
    'reasonable_quantity_range': (5, 50),
    
    # الحد الأدنى للتفرد
    'min_uniqueness_ratio': 0.7
}

def get_site_config(url):
    """الحصول على إعدادات موقع معين"""
    from urllib.parse import urlparse
    
    domain = urlparse(url).netloc.lower()
    
    # البحث عن إعدادات مطابقة
    for site_domain, config in SITE_SPECIFIC_RULES.items():
        if site_domain in domain:
            return config
    
    # إرجاع إعدادات افتراضية
    return {
        'selectors': ['.post', '.entry', '.article', '.item', '.card'],
        'title_selectors': ['h1', 'h2', 'h3', '.title', '.name'],
        'image_selectors': ['img'],
        'link_selectors': ['a'],
        'pagination_selectors': ['.pagination a', '.next', '.more']
    }

def is_valid_content_url(url):
    """التحقق من صحة رابط المحتوى"""
    import re
    
    url_lower = url.lower()
    
    # التحقق من أنماط المحتوى
    movie_patterns = URL_PATTERNS['movie_pages'] + URL_PATTERNS['series_pages']
    
    for pattern in movie_patterns:
        if re.search(pattern, url_lower):
            return True
    
    return False

def clean_title(title):
    """تنظيف العنوان"""
    import re
    
    if not title:
        return ""
    
    cleaned = title.strip()
    
    if EXTRACTION_SETTINGS['advanced_title_cleaning']:
        # تطبيق قواعد التنظيف
        for pattern in CLEANING_RULES['title_cleaning_patterns']:
            cleaned = re.sub(pattern, ' ', cleaned, flags=re.IGNORECASE)
        
        # إزالة النصوص غير المرغوبة
        for remove_text in CLEANING_RULES['remove_from_titles']:
            cleaned = re.sub(rf'\b{remove_text}\b', '', cleaned, flags=re.IGNORECASE)
    
    # تنظيف المسافات
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    
    # التحقق من الطول
    min_len = CLEANING_RULES['min_cleaned_title_length']
    max_len = CLEANING_RULES['max_cleaned_title_length']
    
    if len(cleaned) < min_len or len(cleaned) > max_len:
        return ""
    
    return cleaned
